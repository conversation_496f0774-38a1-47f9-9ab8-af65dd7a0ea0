const prompts = {
    // 核心設定 - 完善模式
    enhanceCore: (idea, existingData, context) => `
基於用戶的創意想法和現有設定，幫我完善小說的基礎設定：

【用戶創意想法】
${idea}

【現有設定】
書名：${existingData.title || '(空白)'}
類型：${existingData.genre || '(空白)'}
風格：${existingData.style || '(空白)'}
核心：${existingData.core || '(空白)'}

請根據現有內容進行完善和擴展，保持一致性。對於空白欄位請生成內容，對於已有內容請進行深化和完善。

請按照以下格式返回完整的基礎設定：
書名：[如果原為空則生成，否則保留或優化]
類型：[完善的小說類型，用逗號分隔]
風格：[詳細的寫作風格描述]
核心：[深化的故事核心主題和衝突]

要求：
1. 必須填寫所有四個欄位，不可留空或使用佔位符
2. 與現有內容保持邏輯一致性
3. 每個欄位都要有實質性的詳細內容
4. 所有回應內容必須使用繁體中文`,

    // 核心設定 - 生成模式
    generateCore: (idea, context) => `
根據用戶的創意想法，重新生成完整的小說基礎設定：

【用戶創意想法】
${idea}

請忽略任何現有設定，重新創建一套完整且邏輯自洽的基礎設定。

請按照以下格式返回：
書名：[全新的吸引人書名]
類型：[準確的小說類型，用逗號分隔]
風格：[具體的寫作風格描述]
核心：[清晰的故事核心主題和主要衝突]

要求：
1. 必須填寫所有四個欄位，內容要豐富詳細
2. 所有設定要邏輯自洽，相互呼應
3. 每個欄位至少20字以上的實質內容
4. 所有回應內容必須使用繁體中文`,

    fillCore: (idea) => `
根據以下創意想法，幫我生成小說的基礎設定：

創意想法：${idea}

請按照以下格式返回：
書名：[建議的書名]
類型：[小說類型，用逗號分隔]
風格：[寫作風格描述]
核心：[故事核心主題]

要求：
1. 書名要吸引人且符合內容
2. 類型要準確反映故事特點
3. 風格要具體可執行
4. 核心要深刻有意義`,

    // 世界觀 - 完善模式
    enhanceWorld: (context, worldContext, existingWorldData) => `
基於小說設定和現有的世界觀內容，幫我完善和擴展世界觀的各個方面：

【小說基本設定】
${context}

【現有世界觀內容】
${worldContext}

【詳細現有內容】
地理環境：
- 大陸地形：${existingWorldData.geography || '(空白)'}
- 氣候環境：${existingWorldData.climate || '(空白)'}
- 重要地點：${existingWorldData.locations || '(空白)'}

力量體系：
- 修煉體系：${existingWorldData.powerSystem || '(空白)'}
- 力量來源：${existingWorldData.powerSource || '(空白)'}
- 修煉規則：${existingWorldData.powerRules || '(空白)'}

科技文明：
- 科技水平：${existingWorldData.techLevel || '(空白)'}
- 文明發展：${existingWorldData.civilization || '(空白)'}

社會結構：
- 階級制度：${existingWorldData.socialStructure || '(空白)'}
- 重要勢力：${existingWorldData.organizations || '(空白)'}
- 世界法則：${existingWorldData.worldRules || '(空白)'}

請針對空白或內容不足的部分進行補充和完善，確保各部分之間邏輯一致，不與現有內容衝突。

請按照以下格式返回完整的世界觀設定：
【地理環境】
大陸地形：[完善後的詳細內容，至少50字]
氣候環境：[完善後的詳細內容，至少30字]
重要地點：[完善後的詳細內容，至少80字]

【力量體系】
修煉體系：[完善後的詳細內容，至少60字]
力量來源：[完善後的詳細內容，至少40字]
修煉規則：[完善後的詳細內容，至少50字]

【科技文明】
科技水平：[完善後的詳細內容，至少40字]
文明發展：[完善後的詳細內容，至少50字]

【社會結構】
階級制度：[完善後的詳細內容，至少50字]
重要勢力：[完善後的詳細內容，至少80字]
世界法則：[完善後的詳細內容，至少60字]

要求：
1. 所有11個欄位都必須填寫，不可留空或使用佔位符
2. 每個欄位都要有實質性的詳細內容
3. 保持與現有內容的一致性`,

    // 世界觀 - 生成模式
    generateWorld: (context, worldContext) => `
根據小說的基本設定，重新生成一個完整且邏輯自洽的世界觀。

【小說基本設定】
${context}

請忽略任何現有世界觀內容，重新創建一套完整的世界觀體系。

⚠️ 重要：必須嚴格按照以下格式返回，每個欄位都必須填寫，不可省略任何一個：

【地理環境】
大陸地形：[必填] 詳細描述世界的地理結構，包含主要大陸、山脈、河流等，至少80字的具體內容
氣候環境：[必填] 描述氣候特點和環境條件，與地理相呼應，至少50字的具體內容
重要地點：[必填] 列出8-10個重要地點及其特色，包含宗門、城市、禁地等，至少120字的具體內容

【力量體系】
修煉體系：[必填] 詳細的等級劃分和修煉階段，符合小說類型，至少100字的具體內容
力量來源：[必填] 力量的根本來源和獲取方式，至少60字的具體內容
修煉規則：[必填] 修煉的基本規則、限制和突破條件，至少80字的具體內容

【科技文明】
科技水平：[必填] 科技發展程度和特色技術，與修煉體系協調，至少60字的具體內容
文明發展：[必填] 文明的發展歷程和特點，至少80字的具體內容

【社會結構】
階級制度：[必填] 社會階層劃分，與力量體系相關，至少70字的具體內容
重要勢力：[必填] 主要宗門、組織、勢力及其特色，至少120字的具體內容
世界法則：[必填] 世界運行的基本法則和價值觀，至少80字的具體內容

🔴 絕對禁止：
- 不可留空任何欄位
- 不可使用「待補充」、「...」、「**」等佔位符
- 不可使用少於要求字數的內容
- 每個欄位都必須有實質性的詳細描述

✅ 必須確保：
1. 所有11個欄位都有完整內容
2. 每個欄位都符合最小字數要求
3. 各部分邏輯自洽，相互呼應
4. 符合小說的類型和風格定位`,
    
    fillCharacter: (role, context, worldContext, existingCharacters, glossaryContext = '') => `
根據小說設定和世界觀，為我創建一個全新的角色：

${context}

${worldContext}

${glossaryContext}

【已有角色】
${existingCharacters}

【目標角色定位】
${role}

⚠️ 重要提醒：
1. 請特別注意「創作想法」中的設定，角色必須完全符合創作想法中描述的世界觀、故事背景和人物設定！
2. 如果創作想法中提到了具體的主角設定，請嚴格按照該設定生成角色！
3. 所有回應內容必須使用繁體中文！

請仔細參考以上所有設定信息，創建一個完全符合世界觀和創作想法的角色。特別注意：
- 姓名要符合世界觀的命名風格和文化背景，可參考名詞庫中的人物命名規律
- 外貌要符合世界觀的種族、地域特色，體現重要地點的文化特色
- 性格和背景要與故事核心、類型風格一致，可與名詞庫中的組織勢力有關聯
- 關鍵物品要符合力量體系和科技水平，可參考名詞庫中的武器法寶、功法技能
- 角色定位要符合社會結構和階級制度，與已有勢力組織有合理關係
- 使用名詞庫中的專有術語，保持設定的一致性

請嚴格按照以下格式返回，每行必須以指定標籤開頭，不要添加任何其他內容：

姓名：[符合世界觀命名規則的角色名字]
外貌：[詳細的外貌描述，體現世界觀特色和地域文化]
性格與背景：[性格特點和背景故事，與故事核心和主線劇情相關，符合社會結構設定]
關鍵物品：[符合力量體系和科技水平的重要物品或能力，與角色背景相匹配]

格式示例：
姓名：林凡
外貌：黑髮黑瞳，面容清秀，眼神堅毅，身材修長。
性格與背景：性格堅毅，重情重義。本是天才，後遭人陷害，丹田被廢，成為廢柴。
關鍵物品：神秘龍戒，內含上古傳承。

⚠️ 重要：必須嚴格按照上述格式輸出，每個欄位都要填寫，不可省略任何標籤！不要添加額外的說明或格式！

嚴格要求：
1. 必須嚴格遵循世界觀的所有設定，不可違背任何已定義的規則
2. 角色的一切屬性都要與核心設定保持一致
3. 與主角和其他角色要有合理的關係網絡
4. 在劇情中要有明確且重要的作用
5. 避免與已有角色重複或產生設定衝突
6. 體現出世界觀的獨特性和深度`,

    enhanceCharacter: (characterData, context, worldContext, existingCharacters, glossaryContext = '') => `
基於現有角色資料，幫我完善和擴寫這個角色，讓角色更加豐滿立體：

${context}

${worldContext}

${glossaryContext}

【已有角色】
${existingCharacters}

⚠️ 重要提醒：
1. 請特別注意「創作想法」中的設定，角色必須完全符合創作想法中描述的世界觀、故事背景和人物設定！
2. 如果創作想法中提到了具體的主角設定，請嚴格按照該設定完善角色！
3. 所有回應內容必須使用繁體中文！

【當前角色資料】
姓名：${characterData.name || '(空白)'}
角色定位：${characterData.role || '(空白)'}
外貌：${characterData.appearance || '(空白)'}
性格與背景：${characterData.personality || '(空白)'}
金手指/關鍵物品：${characterData.item || '(空白)'}

請仔細參考核心設定、世界觀和名詞庫的所有信息，在現有內容基礎上進行完善和擴寫。特別注意：
- 確保所有內容都符合世界觀的設定規則，與名詞庫保持一致
- 角色的發展要與故事核心和類型風格一致
- 外貌描述要體現世界觀的文化特色，可參考重要地點的特色
- 性格背景要符合社會結構和階級制度，與組織勢力有合理關聯
- 關鍵物品要符合力量體系和科技水平，可參考名詞庫中的相關設定
- 使用名詞庫中的專有術語，確保設定統一性

請嚴格按照以下格式返回完整角色資料，每行必須以指定標籤開頭，不要添加任何其他內容：

姓名：[如果原為空則根據世界觀生成，否則保留原名]
外貌：[在原有基礎上擴寫或生成，體現世界觀特色和地域文化]
性格與背景：[在原有基礎上擴寫或生成，深化角色背景，符合世界觀設定]
關鍵物品：[在原有基礎上擴寫或生成，嚴格符合力量體系和角色設定]

格式示例：
姓名：蘇雨薇
外貌：容貌絕美，氣質清冷，一襲白衣勝雪，眉宇間帶著淡淡的傲氣。
性格與背景：天才少女，出身名門，性格高傲但內心善良，與主角有著複雜的情感糾葛。
關鍵物品：冰心劍，家族傳承的神兵利器。

⚠️ 重要：必須嚴格按照上述格式輸出，每個欄位都要填寫，不可省略任何標籤！不要添加額外的說明或格式！

嚴格要求：
1. 絕對保持與現有內容的一致性，不可產生任何衝突
2. 對空白欄位進行生成，對有內容的欄位進行深度擴寫
3. 所有內容必須嚴格符合世界觀和故事設定
4. 讓角色更加立體和有深度，同時保持設定的統一性
5. 確保角色在整個世界觀中的定位和作用清晰明確`,

    // 角色單個欄位生成
    generateCharacterField: (field, characterData, context, worldContext, glossaryContext = '') => {
        const fieldPrompts = {
            name: `根據角色定位"${characterData.role || '主角'}"和世界觀設定，為這個角色生成一個合適的姓名。要求：符合世界觀的命名風格和文化背景，可參考名詞庫中的人物命名規律。必須使用繁體中文。只返回姓名，不要其他內容。`,

            appearance: `根據角色"${characterData.name || characterData.role || '角色'}"的設定和世界觀，生成詳細的外貌描述。要求：符合世界觀的種族、地域特色，體現重要地點的文化特色，描述要生動具體。必須使用繁體中文。只返回外貌描述，不要其他內容。`,

            personality: `根據角色"${characterData.name || characterData.role || '角色'}"的設定和世界觀，生成詳細的性格與背景描述。要求：與故事核心、類型風格一致，可與名詞庫中的組織勢力有關聯，符合社會結構和階級制度。必須使用繁體中文。只返回性格與背景描述，不要其他內容。`,

            item: `根據角色"${characterData.name || characterData.role || '角色'}"的設定和世界觀，生成合適的金手指或關鍵物品。要求：符合力量體系和科技水平，可參考名詞庫中的武器法寶、功法技能，與角色背景相匹配。必須使用繁體中文。只返回物品描述，不要其他內容。`
        };

        return `
${context}

${worldContext}

${glossaryContext}

【角色當前信息】
姓名：${characterData.name || '(未設定)'}
角色定位：${characterData.role || '主角'}
外貌：${characterData.appearance || '(未設定)'}
性格與背景：${characterData.personality || '(未設定)'}
關鍵物品：${characterData.item || '(未設定)'}

${fieldPrompts[field] || '生成角色相關內容'}
        `;
    },

    // 生成創作想法
    generateIdea: (context, randomSeed) => `
基於已有的作品基礎信息，為我生成一個有趣且獨特的小說創作想法：

${context}

⚠️ 重要：所有回應內容必須使用繁體中文！

請根據以上已填寫的作品基礎信息，生成一個符合設定的創作想法。要求：
1. 必須與已設定的小說名稱、類型、風格、核心保持一致
2. 如果某些欄位未設定，可以為其提供建議
3. 創作想法要具有獨特性和創新性，避免老套的設定
4. 包含具體的衝突設定和成長路線
5. 適合網絡小說的風格和節奏
6. 字數控制在150-250字

創意指導（隨機種子：${randomSeed}）：
請基於此隨機數字，創造性地組合以下元素，確保每次生成都有獨特性：

金手指類型（選擇1-2個組合）：
- 系統流：遊戲系統、修煉系統、商業系統、戰爭系統
- 重生穿越：回到過去、穿越異界、靈魂互換、時空穿梭
- 特殊能力：讀心術、預知未來、控制時間、空間操控
- 神秘傳承：古老功法、神器認主、血脈覺醒、師父傳承

世界觀背景（創新組合）：
- 修仙+科技：修仙者使用高科技、機甲修仙、星際修煉
- 都市+異能：現代都市中的超能力者、隱藏的異能世界
- 遊戲+現實：虛擬遊戲影響現實、遊戲世界成真
- 末世+重建：災後重建、文明復甦、新秩序建立

主角設定（避免老套）：
- 身份反轉：反派變正派、敵人變盟友、配角變主角
- 特殊職業：遊戲設計師、美食家、醫生、老師、藝術家
- 獨特背景：來自未來、異世界原住民、人工智能、克隆人

衝突創新：
- 多元對立：不同文明碰撞、價值觀衝突、代際矛盾
- 內心掙扎：道德選擇、身份認同、責任與自由
- 時間壓力：倒計時拯救、預言實現、機會窗口

格式：我想寫一個關於...的故事，主角是...，在...的世界中...

⚠️ 重要：每次生成都必須確保內容的獨特性和新穎性，絕對避免重複或相似的設定組合！
    `,

    // 劇情大綱 - 完善模式
    enhanceOutline: (context, worldContext, existingOutlineData) => `
基於小說設定、世界觀和現有大綱，幫我完善劇情大綱：

【小說基本設定】
${context}

【世界觀背景】
${worldContext}

【現有大綱結構】
${JSON.stringify(existingOutlineData, null, 2)}

請根據現有的卷章結構進行完善和擴展，對空白或內容不足的部分進行補充。

請按照以下格式返回完善後的劇情大綱：
第一卷：[完善後的卷名，如果原為空則生成]
第1章：[完善後的章節標題] - [詳細的章節目標和主要情節，至少50字]
第2章：[完善後的章節標題] - [詳細的章節目標和主要情節，至少50字]
...

要求：
1. 保持與現有結構的一致性
2. 所有章節都要有詳細的目標描述
3. 確保劇情邏輯連貫
4. 符合世界觀設定`,

    // 劇情大綱 - 生成模式
    generateOutline: (context, worldContext) => `
根據小說設定和世界觀，重新生成完整的劇情大綱：

【小說基本設定】
${context}

【世界觀背景】
${worldContext}

請忽略任何現有大綱，重新創建一套完整的劇情結構。

請按照以下格式生成劇情大綱：
第一卷：[吸引人的卷名]
第1章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第2章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第3章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第4章：[章節標題] - [詳細的章節目標和主要情節，至少60字]

第二卷：[吸引人的卷名]
第1章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第2章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第3章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第4章：[章節標題] - [詳細的章節目標和主要情節，至少60字]

第三卷：[吸引人的卷名]
第1章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第2章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第3章：[章節標題] - [詳細的章節目標和主要情節，至少60字]
第4章：[章節標題] - [詳細的章節目標和主要情節，至少60字]

要求：
1. 必須生成至少3卷，每卷至少4章
2. 每個章節都要有豐富的內容描述
3. 章節之間要有邏輯連貫性和起承轉合
4. 符合小說的世界觀設定和類型特點`,

    suggestPlot: (context, currentOutline) => `
基於現有的劇情設定，為我提供一些創意的劇情建議：
${context}
${currentOutline}

請提供：
1. 有趣的劇情轉折點
2. 角色關係的發展建議
3. 衝突升級的方式
4. 意想不到的情節安排

要求新穎有創意，符合故事邏輯。`
};
