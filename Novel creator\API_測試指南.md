# AI小說生成器 - API測試指南

## 🔧 問題修復說明

我已經修復了您遇到的兩個主要問題：

### 1. ❌ AI調用失敗404錯誤
**原因：** API端點錯誤和錯誤處理不完善
**修復：**
- 更新了API驗證端點
- 改善了錯誤處理和用戶提示
- 添加了詳細的錯誤信息說明

### 2. ❌ AI生成角色與故事不相關
**原因：** 角色AI功能還在使用固定的模擬數據
**修復：**
- 完全重寫了角色AI功能，使用真實的Gemini API
- 添加了上下文感知，AI會考慮您的故事設定
- 改進了提示詞，確保生成的角色符合您的世界觀

## 🚀 使用步驟

### 第一步：設定API Key
1. 打開應用，進入「核心設定」標籤
2. 在API設定區域輸入您的Gemini API Key
3. 點擊「測試連接」，確保狀態顯示為「已連接」（綠色圓點）

### 第二步：填寫基礎設定
1. 填寫小說名稱、類型等基本信息
2. 在「告訴AI你的初步想法」中描述您的故事概念
3. 點擊「讓AI幫我完善基礎設定」

### 第三步：建構世界觀
1. 切換到「世界觀」標籤
2. 可以手動填寫各個分類，或使用AI功能：
   - 「AI完善世界觀」：基於已有內容進行擴展
   - 「AI生成世界觀」：全新生成完整世界觀

### 第四步：創建角色
1. 切換到「角色設定」標籤
2. 點擊「新增角色」
3. 填寫角色定位（如：主角、女主角、反派等）
4. 點擊「AI輔助填寫此角色」
5. AI會根據您的故事設定生成相關角色

## 🔍 測試要點

### API連接測試
- ✅ API Key輸入後能成功測試連接
- ✅ 狀態指示器顯示正確（綠色=已連接，紅色=未連接）
- ✅ 錯誤時顯示具體的錯誤信息

### 世界觀AI功能測試
- ✅ AI生成的內容會自動填入對應的分類欄位
- ✅ 內容符合您設定的小說類型和風格
- ✅ 各個分類之間邏輯一致

### 角色AI功能測試
- ✅ 生成的角色符合您的世界觀設定
- ✅ 角色與主線劇情相關
- ✅ 考慮了已有角色，避免重複

## 🛠️ 故障排除

### 如果仍然出現404錯誤：
1. 檢查API Key是否正確
2. 確認網絡連接正常
3. 嘗試重新獲取API Key

### 如果AI生成內容不相關：
1. 確保在「核心設定」中填寫了詳細的故事信息
2. 在世界觀中提供更多背景信息
3. 角色定位要明確（主角、配角、反派等）

### 如果API配額用完：
- 錯誤信息會顯示「配額已用完」
- 需要等待配額重置或升級API計劃

## 📝 使用技巧

1. **詳細描述**：在「初步想法」中提供越詳細的描述，AI生成的內容越準確
2. **分步驟填寫**：先完善核心設定和世界觀，再創建角色
3. **多次調用**：如果第一次生成的內容不滿意，可以修改設定後重新生成
4. **手動調整**：AI生成後可以手動修改和完善內容

## 🎯 預期效果

修復後，您應該能夠：
- ✅ 成功連接Gemini API
- ✅ 生成符合您故事設定的世界觀內容
- ✅ 創建與您的故事相關的角色
- ✅ 獲得結構化、分類清晰的內容

如果還有任何問題，請檢查瀏覽器控制台的錯誤信息，這將幫助進一步診斷問題。
