class NovelGenerator {
    constructor() {
        // 初始化各個模組
        this.state = new AppState();
        this.ui = new UI();
        this.api = new API();
        this.generatedIdeas = new Set(); // 記錄已生成的想法，避免重複

        // 將實例掛載到 window，方便在模組間互相調用和調試
        window.app = this;
        window.ui = this.ui; // 讓 state.js 能調用
    }

    /**
     * 初始化應用程式
     */
    init() {
        this.ui.init();
        this.api.init();
        this.state.loadStateFromLocalStorage();
        this.state.applyStateToDOM();
        this.ui.updateProgress();
        this.bindEvents();

        console.log('AI Novel Generator 已初始化完成 (v2.0 Refactored)');
    }

    /**
     * 綁定所有事件監聽器
     */
    bindEvents() {
        try {
            // 使用事件委派來處理整個容器內的事件
            if (this.ui.elements.container) {
                this.ui.elements.container.addEventListener('click', (e) => this.handleContainerClick(e));
                this.ui.elements.container.addEventListener('input', Utils.debounce(() => {
                    try {
                        this.state.loadStateFromDOM();
                        this.state.saveStateToLocalStorage();
                        this.ui.updateProgress();
                    } catch (error) {
                        console.error('Error during input handling:', error);
                        this.ui.showNotification('數據保存時發生錯誤', 'error');
                    }
                }, 500));
            }

            // 模態框事件
            if (this.ui.elements.modal) {
                this.ui.elements.modal.addEventListener('click', (e) => this.handleModalClick(e));
            }

            // 導入/導出/主題
            const exportBtn = document.getElementById('exportBtn');
            const importBtn = document.getElementById('importBtn');
            const importFile = document.getElementById('importFile');

            if (exportBtn) exportBtn.addEventListener('click', () => this.exportData());
            if (importBtn) importBtn.addEventListener('click', () => {
                const fileInput = document.getElementById('importFile');
                if (fileInput) fileInput.click();
            });
            if (importFile) importFile.addEventListener('change', (e) => this.handleFileImport(e));
            if (this.ui.elements.themeToggle) {
                this.ui.elements.themeToggle.addEventListener('click', () => this.ui.toggleTheme());
            }

            // 快捷鍵
            document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        } catch (error) {
            console.error('Error binding events:', error);
            this.ui.showNotification('初始化事件監聽器時發生錯誤', 'error');
        }
    }

    /**
     * 統一處理主容器內的點擊事件
     */
    handleContainerClick(e) {
        try {
            const target = e.target;

            // 頁籤切換
            const tabButton = target.closest('.tab-button');
            if (tabButton && tabButton.dataset.tab) {
                this.ui.handleTabSwitch(tabButton.dataset.tab);
                return;
            }

            // 角色卡折疊 - 現在通過onclick處理，這裡不需要處理了

            // 所有按鈕點擊
            const button = target.closest('button');
            if (!button) return;

            // --- 更健壯的事件處理 ---
            // 優先處理帶有特定 class 的動態按鈕
            if (button.classList.contains('remove-character')) {
                console.log('Remove character button clicked, id:', button.dataset.id);
                if (button.dataset.id) {
                    this.ui.removeCharacter(button.dataset.id);
                } else {
                    console.error('Remove character button missing data-id');
                    this.ui.showNotification('刪除角色失敗：缺少角色ID', 'error');
                }
                return;
            }
            if (button.classList.contains('remove-volume')) {
                if (confirm('確定要刪除此卷及其所有章節嗎？')) {
                    const volumeCard = button.closest('.volume-card');
                    if (volumeCard) {
                        volumeCard.remove();
                        // 更新狀態
                        this.state.loadStateFromDOM();
                        this.state.saveStateToLocalStorage();
                    }
                }
                return;
            }
            if (button.classList.contains('remove-chapter')) {
                const chapterCard = button.closest('.chapter-card');
                if (chapterCard) {
                    chapterCard.remove();
                    // 更新狀態
                    this.state.loadStateFromDOM();
                    this.state.saveStateToLocalStorage();
                }
                return;
            }
            if (button.classList.contains('add-chapter')) {
                const volumeEl = button.closest('.volume-card');
                if (volumeEl) {
                    const newChapter = this.ui.addChapter(volumeEl);
                    const input = newChapter.querySelector('input');
                    if (input) input.focus();
                }
                return;
            }
            if (button.classList.contains('ai-button')) {
                console.log('AI button clicked:', button.id, button.className);
                this.handleAIActions(button);
                return;
            }

            // 最後處理帶有固定ID的靜態按鈕
            switch (button.id) {
            case 'add-character':
                this.ui.addCharacterCard();
                break;
            case 'add-volume':
                const newVolume = this.ui.addVolume();
                this.ui.initSortable(); // 重新初始化
                newVolume.querySelector('input').focus();
                break;
            case 'generateNovelBtn':
                this.ui.toggleGenerationModal(true);
                break;
            case 'save-api':
                this.api.saveAPISettings();
                break;
            case 'test-api':
                this.api.testAPIConnection();
                break;
            case 'test-endpoints':
                this.api.toggleDebugPanel(); // 自動打開除錯面板
                this.api.testAllEndpoints();
                break;
            case 'toggle-debug':
                this.api.toggleDebugPanel();
                break;
            case 'clear-debug':
                this.api.clearDebugLog();
                break;
            }
        } catch (error) {
            console.error('Error handling container click:', error);
            this.ui.showNotification('處理點擊事件時發生錯誤', 'error');
        }
    }
    
    /**
     * 處理 AI 相關按鈕點擊
     */
    async handleAIActions(button) {
        console.log('handleAIActions called with button:', button.id);

        if (!this.api.isReady()) {
            this.ui.showNotification('請先在核心設定中配置並測試 API Key', 'error');
            this.ui.handleTabSwitch('core');
            return;
        }

        this.ui.showNotification('AI 正在思考中...', 'info');
        button.disabled = true;

        let prompt;
        try {
            this.state.loadStateFromDOM();
            const context = this.getContextForAI();
            const worldContext = this.getWorldContextForAI();
            const id = button.id;

            console.log('Processing AI action for button ID:', id);

            // 根據按鈕ID或class來決定執行的AI操作
            // 核心設定AI處理
            if (id === 'ai-enhance-core') {
                const idea = this.state.data['ai-idea'];
                if (!idea) { this.ui.showNotification('請先輸入你的初步想法', 'error'); return; }
                const existingCoreData = this.getExistingCoreData();
                prompt = prompts.enhanceCore(idea, existingCoreData, context);
            } else if (id === 'ai-generate-core') {
                const idea = this.state.data['ai-idea'];
                if (!idea) { this.ui.showNotification('請先輸入你的初步想法', 'error'); return; }
                prompt = prompts.generateCore(idea, context);
            }
            // 世界觀AI處理
            else if (id === 'ai-enhance-world') {
                const existingWorldData = this.getExistingWorldData();
                prompt = prompts.enhanceWorld(context, worldContext, existingWorldData);
            } else if (id === 'ai-generate-world') {
                prompt = prompts.generateWorld(context, worldContext);
            }
            // 劇情大綱AI處理
            else if (id === 'ai-enhance-outline') {
                const existingOutlineData = this.getExistingOutlineData();
                prompt = prompts.enhanceOutline(context, worldContext, existingOutlineData);
            } else if (id === 'ai-generate-outline') {
                prompt = prompts.generateOutline(context, worldContext);
            } else if (id === 'ai-generate-idea') {
                // 生成隨機種子以增加創意多樣性
                const randomSeed = Math.floor(Math.random() * 10000) + Date.now() % 10000;
                prompt = prompts.generateIdea(context, randomSeed);
            } else if (button.classList.contains('ai-generate-field')) {
                const field = button.dataset.field;
                const characterId = button.dataset.characterId;
                const card = document.getElementById(`character-${characterId}`);
                const characterData = {
                    name: card.querySelector('[data-field="name"]').value,
                    role: card.querySelector('[data-field="role"]').value,
                    appearance: card.querySelector('[data-field="appearance"]').value,
                    personality: card.querySelector('[data-field="personality"]').value,
                    item: card.querySelector('[data-field="item"]').value
                };
                const glossaryContext = this.getGlossaryContextForAI();
                prompt = prompts.generateCharacterField(field, characterData, context, worldContext, glossaryContext);
            } else if (button.classList.contains('ai-generate-character')) {
                const card = button.closest('.character-card');
                const characterData = {
                    name: card.querySelector('[data-field="name"]').value,
                    role: card.querySelector('[data-field="role"]').value,
                    appearance: card.querySelector('[data-field="appearance"]').value,
                    personality: card.querySelector('[data-field="personality"]').value,
                    item: card.querySelector('[data-field="item"]').value
                };
                const existingCharacters = this.getCharacterContextForAI();
                const glossaryContext = this.getGlossaryContextForAI();
                const role = characterData.role || '主角';
                prompt = prompts.fillCharacter(role, context, worldContext, existingCharacters, glossaryContext);
                console.log('角色完全生成模式，角色定位:', role);
            } else if (button.classList.contains('ai-fill-character')) {
                const card = button.closest('.character-card');
                const characterData = {
                    name: card.querySelector('[data-field="name"]').value,
                    role: card.querySelector('[data-field="role"]').value,
                    appearance: card.querySelector('[data-field="appearance"]').value,
                    personality: card.querySelector('[data-field="personality"]').value,
                    item: card.querySelector('[data-field="item"]').value
                };
                const existingCharacters = this.getCharacterContextForAI();
                const glossaryContext = this.getGlossaryContextForAI();
                const hasContent = Object.values(characterData).some(value => value && value.length > 0);

                if (hasContent) {
                    // 完善模式：基於現有內容進行擴寫
                    prompt = prompts.enhanceCharacter(characterData, context, worldContext, existingCharacters, glossaryContext);
                    console.log('角色完善模式，現有內容:', characterData);
                } else {
                    // 生成模式：創建全新角色
                    const role = characterData.role || '主角';
                    prompt = prompts.fillCharacter(role, context, worldContext, existingCharacters, glossaryContext);
                    console.log('角色生成模式，角色定位:', role);
                }
            }

            if (prompt) {
                const result = await this.api.call(prompt);
                this.handleAIResult(button, result);
                this.ui.showNotification('AI 已完成操作！', 'success');
            } else {
                // 如果沒有生成prompt，說明是一個未知的AI按鈕
                this.ui.showNotification('未實現的AI功能', 'error');
            }
        } catch (error) {
            this.ui.showNotification(`AI 操作失敗: ${error.message}`, 'error');
        } finally {
            button.disabled = false;
        }
    }

    /**
     * 處理 AI 返回的結果
     */
    handleAIResult(button, result) {
        console.log('🔄 handleAIResult 被調用');
        console.log('按鈕ID:', button.id);
        console.log('按鈕類名:', button.className);
        console.log('結果長度:', result.length);

        const id = button.id;

        if (id === 'ai-enhance-core' || id === 'ai-generate-core') {
            this.parseAndFillCoreSettings(result);
        } else if (id === 'ai-generate-idea') {
            const trimmedResult = result.trim();

            // 檢查是否與之前生成的想法重複
            if (this.generatedIdeas.has(trimmedResult)) {
                this.ui.showNotification('生成的想法與之前重複，請重新生成', 'warning');
                return;
            }

            // 記錄新生成的想法
            this.generatedIdeas.add(trimmedResult);

            // 如果記錄的想法太多，清理舊的（保留最近20個）
            if (this.generatedIdeas.size > 20) {
                const ideasArray = Array.from(this.generatedIdeas);
                this.generatedIdeas.clear();
                ideasArray.slice(-15).forEach(idea => this.generatedIdeas.add(idea));
            }

            document.getElementById('ai-idea').value = trimmedResult;
        } else if (id === 'ai-enhance-world') {
            this.parseAndFillWorldSettings(result, 'enhance');
        } else if (id === 'ai-generate-world') {
            this.parseAndFillWorldSettings(result, 'generate');
        } else if (id === 'ai-enhance-outline' || id === 'ai-generate-outline') {
            this.parseAndFillOutline(result);
        } else if (button.classList.contains('ai-generate-field')) {
            const field = button.dataset.field;
            const characterId = button.dataset.characterId;
            const card = document.getElementById(`character-${characterId}`);
            const input = card.querySelector(`[data-field="${field}"]`);
            if (input) {
                input.value = result.trim();
                if (field === 'name') {
                    this.ui.updateCharacterNameDisplay(characterId, result.trim());
                }
            }
        } else if (button.classList.contains('ai-fill-character') || button.classList.contains('ai-generate-character')) {
            console.log('🎯 角色AI按鈕被點擊，開始處理結果...');
            const card = button.closest('.character-card');
            if (!card) {
                console.error('❌ 找不到角色卡片！');
                this.ui.showNotification('錯誤：找不到角色卡片', 'error');
                return;
            }
            console.log('✅ 找到角色卡片，開始解析數據...');
            this.parseAndFillCharacterData(result, card);
        }
        
        // 操作完成後，自動保存數據
        this.state.loadStateFromDOM();
        this.state.saveStateToLocalStorage();
        this.ui.updateProgress();
    }

    /**
     * 為 AI 準備上下文
     */
    getContextForAI() {
        const d = this.state.data;
        return `
【小說核心設定】
書名：${d['novel-title'] || '未設定'}
類型：${d['novel-genre'] || '未設定'}
寫作風格：${d['writing-style'] || '未設定'}
故事核心：${d['story-core'] || '未設定'}
創作想法：${d['ai-idea'] || '未設定'}
        `;
    }

    getWorldContextForAI() {
         const d = this.state.data;
         return `
【世界觀設定】
地理環境：
- 大陸地形：${d['world-geography'] || '未設定'}
- 氣候環境：${d['world-climate'] || '未設定'}
- 重要地點：${d['world-locations'] || '未設定'}

力量體系：
- 修煉體系：${d['power-system'] || '未設定'}
- 力量來源：${d['power-source'] || '未設定'}
- 修煉規則：${d['power-rules'] || '未設定'}

科技文明：
- 科技水平：${d['tech-level'] || '未設定'}
- 文明發展：${d['civilization'] || '未設定'}

社會結構：
- 階級制度：${d['social-structure'] || '未設定'}
- 重要勢力：${d['organizations'] || '未設定'}
- 世界法則：${d['world-rules'] || '未設定'}
         `;
    }

    getCharacterContextForAI() {
        const characters = this.state.data.characters || [];
        if (characters.length === 0) return "目前沒有其他角色。";
        return "已有角色列表：\n" + characters.map(c => `- ${c.name} (${c.role})`).join('\n');
    }

    /**
     * 獲取名詞庫信息用於AI參考
     */
    getGlossaryContextForAI() {
        const d = this.state.data;
        return `
【名詞庫參考】
人物名錄：
- 主要人物：${d['characters-main'] || '未設定'}
- 配角路人：${d['characters-supporting'] || '未設定'}

地點名錄：
- 重要地點：${d['locations-major'] || '未設定'}
- 次要地點：${d['locations-minor'] || '未設定'}

物品道具：
- 武器法寶：${d['items-weapons'] || '未設定'}
- 丹藥材料：${d['items-consumables'] || '未設定'}

功法技能：
- 修煉功法：${d['skills-cultivation'] || '未設定'}
- 戰鬥技能：${d['skills-combat'] || '未設定'}

組織勢力：
- 正道勢力：${d['organizations-righteous'] || '未設定'}
- 邪道勢力：${d['organizations-evil'] || '未設定'}

專有術語：
- 修煉術語：${d['terms-cultivation'] || '未設定'}
- 世界術語：${d['terms-world'] || '未設定'}
        `;
    }

    /**
     * 獲取現有世界觀數據
     */
    getExistingWorldData() {
        const d = this.state.data;
        return {
            // 地理環境
            geography: d['world-geography'] || '',
            climate: d['world-climate'] || '',
            locations: d['world-locations'] || '',
            // 力量體系
            powerSystem: d['power-system'] || '',
            powerSource: d['power-source'] || '',
            powerRules: d['power-rules'] || '',
            // 科技文明
            techLevel: d['tech-level'] || '',
            civilization: d['civilization'] || '',
            // 社會結構
            socialStructure: d['social-structure'] || '',
            organizations: d['organizations'] || '',
            worldRules: d['world-rules'] || ''
        };
    }

    /**
     * 檢查是否有世界觀內容
     */
    hasWorldContent(worldData) {
        return Object.values(worldData).some(value => value && value.trim().length > 0);
    }

    /**
     * 獲取現有核心設定數據
     */
    getExistingCoreData() {
        const d = this.state.data;
        return {
            title: d['novel-title'] || '',
            genre: d['novel-genre'] || '',
            style: d['writing-style'] || '',
            core: d['story-core'] || ''
        };
    }

    /**
     * 獲取現有劇情大綱數據
     */
    getExistingOutlineData() {
        // 從DOM中獲取當前的卷章結構
        const volumes = [];
        const volumeElements = document.querySelectorAll('.volume-card');

        volumeElements.forEach(volumeEl => {
            const volumeTitle = volumeEl.querySelector('.volume-title').value || '';
            const chapters = [];

            const chapterElements = volumeEl.querySelectorAll('.chapter-card');
            chapterElements.forEach(chapterEl => {
                const chapterTitle = chapterEl.querySelector('.chapter-title').value || '';
                const chapterGoal = chapterEl.querySelector('.chapter-goal').value || '';
                if (chapterTitle || chapterGoal) {
                    chapters.push({ title: chapterTitle, goal: chapterGoal });
                }
            });

            if (volumeTitle || chapters.length > 0) {
                volumes.push({ title: volumeTitle, chapters });
            }
        });

        return { volumes };
    }

    /**
     * 解析並填充大綱設定
     */
    parseAndFillOutline(result) {
        console.log("--- AI Generated Outline ---");
        console.log(result);

        const lines = result.split('\n').filter(line => line.trim() !== '');
        let currentVolumeEl = null;
        let currentChapterEl = null;

        // 清空現有大綱
        document.getElementById('volumes-container').innerHTML = '';

        lines.forEach(line => {
            const trimmedLine = line.trim();
            // 更強大的正規表示式，能匹配多種卷格式
            const volMatch = trimmedLine.match(/^(?:第[一二三四五六七八九十百千萬\d]+\s*卷|卷\s*[一二三四五六七八九十百千萬\d]+|【.*?】)[:：\s]*(.*)/);
            if (volMatch && volMatch[1]) {
                currentVolumeEl = this.ui.addVolume({ title: trimmedLine });
                currentChapterEl = null; // 新卷開始，重置當前章節
                return;
            }

            // 更強大的正規表示式，能匹配多種章格式
            const chapMatch = trimmedLine.match(/^-*\s*(?:第\s*\d+\s*章|第\s*[一二三四五六七八九十百千萬\d]+\s*章|章\s*\d+)[:：\s]*(.*)/);
            if (chapMatch && chapMatch[1]) {
                if (!currentVolumeEl) {
                    currentVolumeEl = this.ui.addVolume({ title: "第一卷：故事開端" });
                }
                currentChapterEl = this.ui.addChapter(currentVolumeEl, { title: trimmedLine });
                return;
            }

            // 如果不是卷標題也不是章標題，就當作是當前章節的描述
            if (currentChapterEl) {
                const goalTextarea = currentChapterEl.querySelector('[data-field="goal"]');
                if (goalTextarea) {
                    goalTextarea.value += (goalTextarea.value ? '\n' : '') + trimmedLine;
                }
            }
        });

        this.ui.initSortable(); // 全部生成完畢後，重新初始化拖曳功能

        // 驗證生成的大綱完整性
        const volumes = document.querySelectorAll('.volume-card');
        const chapters = document.querySelectorAll('.chapter-card');
        let validChapters = 0;

        chapters.forEach(chapter => {
            const title = chapter.querySelector('.chapter-title').value;
            const goal = chapter.querySelector('.chapter-goal').value;
            if (title && goal && goal.length >= 20) {
                validChapters++;
            }
        });

        if (volumes.length < 2 || chapters.length < 6) {
            this.ui.showNotification(`大綱生成不完整：只有 ${volumes.length} 卷 ${chapters.length} 章，建議重新生成`, 'warning');
        } else if (validChapters < chapters.length * 0.8) {
            this.ui.showNotification(`大綱內容不完整：有 ${chapters.length - validChapters} 章缺少詳細描述`, 'warning');
        } else {
            this.ui.showNotification(`成功生成 ${volumes.length} 卷 ${chapters.length} 章的完整大綱`, 'success');
        }
    }

    /**
     * 解析並填充核心設定
     */
    parseAndFillCoreSettings(result) {
        console.log("--- AI Generated Core Settings ---");
        console.log(result);

        const lines = result.split('\n');
        const data = {};

        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine.includes('書名：')) {
                data['novel-title'] = trimmedLine.split('書名：')[1].trim().replace(/^\[|\]$/g, '');
            } else if (trimmedLine.includes('類型：')) {
                data['novel-genre'] = trimmedLine.split('類型：')[1].trim().replace(/^\[|\]$/g, '');
            } else if (trimmedLine.includes('風格：')) {
                data['writing-style'] = trimmedLine.split('風格：')[1].trim().replace(/^\[|\]$/g, '');
            } else if (trimmedLine.includes('核心：')) {
                data['story-core'] = trimmedLine.split('核心：')[1].trim().replace(/^\[|\]$/g, '');
            }
        });

        // 驗證所有必要欄位是否都有內容
        const requiredFields = ['novel-title', 'novel-genre', 'writing-style', 'story-core'];
        const missingFields = [];
        const invalidFields = [];

        requiredFields.forEach(field => {
            if (!data[field]) {
                missingFields.push(field);
            } else if (data[field].length < 5 || data[field].includes('*') || data[field].includes('...')) {
                invalidFields.push(field);
            }
        });

        if (missingFields.length > 0 || invalidFields.length > 0) {
            console.warn('AI回應不完整:', { missingFields, invalidFields });
            this.ui.showNotification(`AI生成不完整，缺少或無效的欄位：${[...missingFields, ...invalidFields].join(', ')}`, 'warning');
        }

        // 填充到對應的輸入框
        Object.entries(data).forEach(([field, value]) => {
            const element = document.getElementById(field);
            if (element && value && value.length >= 5) {
                element.value = value;
            }
        });

        // 顯示處理結果
        const validFields = Object.keys(data).filter(field => data[field] && data[field].length >= 5);
        this.ui.showNotification(`已處理核心設定的 ${validFields.length}/4 個欄位`, validFields.length === 4 ? 'success' : 'warning');
    }

    /**
     * 解析並填充世界觀設定
     */
    parseAndFillWorldSettings(result, mode = 'enhance') {
        console.log("--- AI Generated World Settings ---");
        console.log(result);

        const lines = result.split('\n');
        const data = {};

        // 更健壯的解析邏輯
        let currentField = null;
        let currentContent = '';

        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return;

            // 檢查是否是欄位標題
            const fieldMappings = {
                '大陸地形：': 'world-geography',
                '氣候環境：': 'world-climate',
                '重要地點：': 'world-locations',
                '修煉體系：': 'power-system',
                '力量來源：': 'power-source',
                '修煉規則：': 'power-rules',
                '科技水平：': 'tech-level',
                '文明發展：': 'civilization',
                '階級制度：': 'social-structure',
                '重要勢力：': 'organizations',
                '世界法則：': 'world-rules'
            };

            let foundField = null;
            for (const [label, fieldId] of Object.entries(fieldMappings)) {
                if (trimmedLine.includes(label)) {
                    foundField = fieldId;
                    // 保存之前的欄位內容
                    if (currentField && currentContent.trim()) {
                        data[currentField] = currentContent.trim().replace(/^\[|\]$/g, '').replace(/^\[必填\]\s*/, '');
                    }
                    // 開始新欄位
                    currentField = fieldId;
                    currentContent = trimmedLine.split(label)[1].trim();
                    break;
                }
            }

            // 如果不是欄位標題，且當前有活動欄位，則追加內容
            if (!foundField && currentField && trimmedLine !== '【地理環境】' && trimmedLine !== '【力量體系】' && trimmedLine !== '【科技文明】' && trimmedLine !== '【社會結構】') {
                currentContent += '\n' + trimmedLine;
            }
        });

        // 保存最後一個欄位的內容
        if (currentField && currentContent.trim()) {
            data[currentField] = currentContent.trim().replace(/^\[|\]$/g, '').replace(/^\[必填\]\s*/, '');
        }

        // 根據模式決定填充策略
        Object.entries(data).forEach(([field, value]) => {
            const element = document.getElementById(field);
            if (element && value) {
                const currentValue = element.value.trim();

                if (mode === 'generate') {
                    // 生成模式：直接覆蓋所有欄位
                    element.value = value;
                    console.log(`生成模式 - 覆蓋欄位 ${field}:`, value);
                } else {
                    // 完善模式：智能填充策略
                    if (!currentValue) {
                        // 如果欄位為空，直接填入
                        element.value = value;
                        console.log(`完善模式 - 填充空白欄位 ${field}:`, value);
                    } else if (currentValue.length < 20) {
                        // 如果欄位有內容但很少，進行完善
                        element.value = currentValue + '\n\n' + value;
                        console.log(`完善模式 - 擴展欄位 ${field}:`, currentValue, '->', element.value);
                    } else {
                        // 如果欄位已有豐富內容，不覆蓋
                        console.log(`完善模式 - 保留現有內容 ${field}:`, currentValue);
                    }
                }
            }
        });

        // 驗證所有世界觀欄位
        const requiredWorldFields = [
            'world-geography', 'world-climate', 'world-locations',
            'power-system', 'power-source', 'power-rules',
            'tech-level', 'civilization', 'social-structure',
            'organizations', 'world-rules'
        ];

        const missingFields = [];
        const invalidFields = [];

        requiredWorldFields.forEach(field => {
            if (!data[field]) {
                missingFields.push(field);
            } else if (data[field].length < 10 || data[field].includes('**') || data[field].includes('...')) {
                invalidFields.push(field);
            }
        });

        if (missingFields.length > 0 || invalidFields.length > 0) {
            console.error('世界觀AI回應不完整:', {
                missingFields,
                invalidFields,
                parsedData: data,
                originalResponse: result
            });

            const problemFields = [...missingFields, ...invalidFields];
            this.ui.showNotification(
                `⚠️ 世界觀生成失敗！缺少或無效的欄位：${problemFields.join(', ')}。請重新點擊「AI輔助生成」`,
                'error'
            );

            // 如果是生成模式且失敗，建議用戶重試
            if (mode === 'generate') {
                console.log('建議重新生成世界觀，當前解析結果不完整');
            }
        }

        // 顯示填充結果
        const validFields = Object.keys(data).filter(field => data[field] && data[field].length >= 10);
        const successRate = validFields.length / 11;

        if (successRate === 1) {
            this.ui.showNotification(`✅ 成功處理世界觀的所有 11 個欄位`, 'success');
        } else if (successRate >= 0.8) {
            this.ui.showNotification(`⚠️ 已處理世界觀的 ${validFields.length}/11 個欄位，建議重新生成缺失的欄位`, 'warning');
        } else {
            this.ui.showNotification(`❌ 世界觀生成不完整，只處理了 ${validFields.length}/11 個欄位，請重新生成`, 'error');
        }
    }

    /**
     * 解析並填充角色設定
     */
    parseAndFillCharacterData(result, card) {
        console.log("🚀 parseAndFillCharacterData 方法被調用！");
        console.log("=== AI 角色生成原始回應 ===");
        console.log(result);
        console.log("=== 回應長度:", result.length, "字符 ===");

        // 立即顯示一個通知，確認方法被調用
        this.ui.showNotification('正在解析角色數據...', 'info');

        const data = {};
        const lines = result.split('\n');

        lines.forEach((line, index) => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return;

            console.log(`解析第${index + 1}行: "${trimmedLine}"`);

            // 更健壯的解析邏輯，支持多種格式
            if (trimmedLine.includes('姓名：') || trimmedLine.includes('姓名:')) {
                const parts = trimmedLine.split(/姓名[：:]/);
                if (parts.length > 1) {
                    data.name = parts[1].trim().replace(/^\[|\]$/g, '');
                    console.log(`解析到姓名: "${data.name}"`);
                }
            }
            else if (trimmedLine.includes('外貌：') || trimmedLine.includes('外貌:')) {
                const parts = trimmedLine.split(/外貌[：:]/);
                if (parts.length > 1) {
                    data.appearance = parts[1].trim().replace(/^\[|\]$/g, '');
                    console.log(`解析到外貌: "${data.appearance}"`);
                }
            }
            else if (trimmedLine.includes('性格與背景：') || trimmedLine.includes('性格與背景:') ||
                     trimmedLine.includes('性格和背景：') || trimmedLine.includes('性格和背景:')) {
                // 修復正則表達式
                let parts;
                if (trimmedLine.includes('性格與背景：')) {
                    parts = trimmedLine.split('性格與背景：');
                } else if (trimmedLine.includes('性格與背景:')) {
                    parts = trimmedLine.split('性格與背景:');
                } else if (trimmedLine.includes('性格和背景：')) {
                    parts = trimmedLine.split('性格和背景：');
                } else if (trimmedLine.includes('性格和背景:')) {
                    parts = trimmedLine.split('性格和背景:');
                }
                if (parts && parts.length > 1) {
                    data.personality = parts[1].trim().replace(/^\[|\]$/g, '');
                    console.log(`解析到性格與背景: "${data.personality}"`);
                }
            }
            else if (trimmedLine.includes('關鍵物品：') || trimmedLine.includes('關鍵物品:') ||
                     trimmedLine.includes('金手指：') || trimmedLine.includes('金手指:')) {
                let parts;
                if (trimmedLine.includes('關鍵物品：')) {
                    parts = trimmedLine.split('關鍵物品：');
                } else if (trimmedLine.includes('關鍵物品:')) {
                    parts = trimmedLine.split('關鍵物品:');
                } else if (trimmedLine.includes('金手指：')) {
                    parts = trimmedLine.split('金手指：');
                } else if (trimmedLine.includes('金手指:')) {
                    parts = trimmedLine.split('金手指:');
                }
                if (parts && parts.length > 1) {
                    data.item = parts[1].trim().replace(/^\[|\]$/g, '');
                    console.log(`解析到關鍵物品: "${data.item}"`);
                }
            }
        });

        console.log("解析後的角色數據:", data);

        // 如果標準解析失敗，嘗試備用解析方法
        if (Object.keys(data).length === 0) {
            console.log("標準解析失敗，嘗試備用解析方法...");
            this.fallbackParseCharacterData(result, data);
        }

        Object.entries(data).forEach(([field, value]) => {
            const input = card.querySelector(`[data-field="${field}"]`);
            console.log(`處理欄位 ${field}:`, input ? '找到輸入框' : '未找到輸入框');

            // 詳細檢查找到的元素
            if (input) {
                console.log(`🔍 找到的元素詳情:`);
                console.log(`   - 標籤名: ${input.tagName}`);
                console.log(`   - ID: ${input.id}`);
                console.log(`   - 類名: ${input.className}`);
                console.log(`   - data-field: ${input.getAttribute('data-field')}`);
                console.log(`   - 當前值: "${input.value}"`);
                console.log(`   - 父元素: ${input.parentElement.tagName}.${input.parentElement.className}`);

                // 高亮顯示這個元素
                const originalStyle = input.style.cssText;
                input.style.cssText = 'background: yellow !important; border: 5px solid red !important; color: black !important;';
                setTimeout(() => {
                    input.style.cssText = originalStyle;
                }, 3000);
            } else {
                console.error(`❌ 找不到 data-field="${field}" 的元素`);
                // 列出卡片內所有有data-field屬性的元素
                const allFields = card.querySelectorAll('[data-field]');
                console.log('卡片內所有data-field元素:', Array.from(allFields).map(el => ({
                    tag: el.tagName,
                    field: el.getAttribute('data-field'),
                    value: el.value
                })));
            }

            if (input && value) {
                const currentValue = input.value.trim();
                console.log(`當前值長度: ${currentValue.length}, 內容: "${currentValue.substring(0, 50)}..."`);

                // 對於AI生成的角色，直接替換所有內容
                input.value = value;
                console.log(`✅ 已設置 ${field} 為:`, value.substring(0, 100) + '...');

                // 強制觸發input事件，確保UI更新
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                if(field === 'name') {
                    const cardId = card.id.split('-')[1];
                    console.log(`更新角色標題顯示，卡片ID: ${cardId}`);
                    this.ui.updateCharacterNameDisplay(cardId, value);
                }

                // 驗證設置是否成功
                setTimeout(() => {
                    const verifyValue = input.value;
                    console.log(`🔍 驗證 ${field} 設置結果:`, verifyValue === value ? '成功' : '失敗');
                    if (verifyValue !== value) {
                        console.error(`❌ ${field} 設置失敗！期望: ${value.substring(0, 50)}..., 實際: ${verifyValue.substring(0, 50)}...`);
                    }

                    // 在頁面上顯示測試信息
                    const testDiv = document.createElement('div');
                    testDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; z-index: 9999; max-width: 300px; font-size: 12px;';
                    testDiv.innerHTML = `
                        <strong>測試 ${field}:</strong><br>
                        值長度: ${verifyValue.length}<br>
                        前50字: ${verifyValue.substring(0, 50)}...<br>
                        <button onclick="this.parentElement.remove()">關閉</button>
                    `;
                    document.body.appendChild(testDiv);

                    // 在輸入框旁邊添加一個明顯的標記
                    const marker = document.createElement('div');
                    marker.style.cssText = 'position: absolute; background: lime; color: black; padding: 5px; z-index: 1000; font-size: 12px; border: 2px solid red;';
                    marker.textContent = `✅ ${field}: ${verifyValue.length}字符`;
                    marker.onclick = () => marker.remove();

                    // 將標記放在輸入框旁邊
                    const inputRect = input.getBoundingClientRect();
                    marker.style.left = (inputRect.right + 10) + 'px';
                    marker.style.top = inputRect.top + 'px';
                    document.body.appendChild(marker);

                    // 5秒後自動移除
                    setTimeout(() => {
                        if (testDiv.parentElement) testDiv.remove();
                        if (marker.parentElement) marker.remove();
                    }, 5000);
                }, 50);
            } else if (!input) {
                console.error(`❌ 找不到欄位 ${field} 的輸入框`);
            } else if (!value) {
                console.warn(`⚠️ 欄位 ${field} 沒有值`);
            }
        });

        // 強制展開角色卡片，確保內容可見
        const content = card.querySelector('.collapsible-content');
        if (content && !content.classList.contains('open')) {
            console.log('🔓 強制展開角色卡片...');
            content.classList.add('open');
            const arrow = card.querySelector('.toggle-arrow');
            if (arrow) arrow.classList.add('rotate-180');
        }

        // 檢查所有輸入框的可見性
        Object.keys(data).forEach(field => {
            const input = card.querySelector(`[data-field="${field}"]`);
            if (input) {
                const computedStyle = window.getComputedStyle(input);
                const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden' && computedStyle.opacity !== '0';
                console.log(`🔍 欄位 ${field} 可見性:`, isVisible ? '可見' : '隱藏');
                console.log(`   - display: ${computedStyle.display}`);
                console.log(`   - visibility: ${computedStyle.visibility}`);
                console.log(`   - opacity: ${computedStyle.opacity}`);

                if (!isVisible) {
                    console.log('🚨 嘗試強制顯示隱藏的輸入框...');
                    input.style.display = 'block';
                    input.style.visibility = 'visible';
                    input.style.opacity = '1';
                }

                // 強制修改樣式，確保文字可見
                console.log('🎨 強制修改輸入框樣式...');
                input.style.color = '#ffffff !important';
                input.style.backgroundColor = '#374151 !important';
                input.style.border = '2px solid #10b981 !important';
                input.style.fontSize = '14px !important';
                input.style.padding = '8px !important';
                input.style.minHeight = '40px !important';

                // 如果是textarea，設置特殊樣式
                if (input.tagName.toLowerCase() === 'textarea') {
                    input.style.minHeight = '80px !important';
                    input.style.resize = 'vertical !important';
                }

                // 強制重新渲染
                input.style.display = 'none';
                input.offsetHeight; // 觸發重排
                input.style.display = input.tagName.toLowerCase() === 'textarea' ? 'block' : 'flex';
            }
        });

        // 創建一個直接可見的測試區域
        const testArea = document.createElement('div');
        testArea.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            color: black;
            padding: 20px;
            border: 3px solid red;
            z-index: 10000;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
            font-family: monospace;
            font-size: 12px;
        `;

        testArea.innerHTML = `
            <h3>🧪 角色生成測試結果</h3>
            <button onclick="this.parentElement.remove()" style="float: right;">關閉</button>
            <hr>
            <strong>姓名:</strong> ${data.name || '無'}<br><br>
            <strong>外貌:</strong> ${data.appearance || '無'}<br><br>
            <strong>性格與背景:</strong> ${data.personality || '無'}<br><br>
            <strong>關鍵物品:</strong> ${data.item || '無'}<br><br>
            <hr>
            <p>如果您能看到這個彈窗和上面的內容，說明AI生成成功了，問題出在輸入框的顯示上。</p>
        `;

        document.body.appendChild(testArea);

        // 10秒後自動關閉
        setTimeout(() => {
            if (testArea.parentElement) testArea.remove();
        }, 10000);

        // 顯示處理結果
        const filledFields = Object.keys(data).filter(field => data[field]);
        console.log(`✅ 成功處理了 ${filledFields.length} 個欄位:`, filledFields);

        if (filledFields.length > 0) {
            this.ui.showNotification(`✅ 成功生成角色的 ${filledFields.length} 個欄位！`, 'success');

            // 強制觸發DOM更新事件
            setTimeout(() => {
                this.state.loadStateFromDOM();
                this.state.saveStateToLocalStorage();
                this.ui.updateProgress();
            }, 100);
        } else {
            console.warn('角色數據解析失敗，原始結果:', result);
            this.ui.showNotification('角色數據解析失敗，請檢查AI回應格式', 'error');
        }
    }

    /**
     * 備用角色數據解析方法
     */
    fallbackParseCharacterData(result, data) {
        console.log("使用備用解析方法...");

        // 嘗試更寬鬆的解析
        const lines = result.split('\n');
        let currentField = null;
        let currentContent = '';

        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return;

            // 檢查是否是欄位標題
            if (trimmedLine.match(/^姓名[：:]/)) {
                if (currentField) data[currentField] = currentContent.trim();
                currentField = 'name';
                currentContent = trimmedLine.replace(/^姓名[：:]/, '').trim();
            } else if (trimmedLine.match(/^外貌[：:]/)) {
                if (currentField) data[currentField] = currentContent.trim();
                currentField = 'appearance';
                currentContent = trimmedLine.replace(/^外貌[：:]/, '').trim();
            } else if (trimmedLine.match(/^性格[與和]?背景[：:]/)) {
                if (currentField) data[currentField] = currentContent.trim();
                currentField = 'personality';
                currentContent = trimmedLine.replace(/^性格[與和]?背景[：:]/, '').trim();
            } else if (trimmedLine.match(/^(?:關鍵物品|金手指)[：:]/)) {
                if (currentField) data[currentField] = currentContent.trim();
                currentField = 'item';
                currentContent = trimmedLine.replace(/^(?:關鍵物品|金手指)[：:]/, '').trim();
            } else if (currentField) {
                // 繼續添加到當前欄位
                currentContent += ' ' + trimmedLine;
            }
        });

        // 處理最後一個欄位
        if (currentField && currentContent.trim()) {
            data[currentField] = currentContent.trim();
        }

        console.log("備用解析結果:", data);
    }

    /**
     * 處理模態框內的點擊事件
     */
    handleModalClick(e) {
        if (e.target.closest('#close-modal')) {
            this.ui.toggleGenerationModal(false);
        }
        if (e.target.closest('#start-generation-btn')) {
            // TODO: 實現生成邏輯
            this.ui.showNotification('正在生成中... (功能待實現)', 'info');
        }
    }

    /**
     * 處理鍵盤快捷鍵
     */
    handleKeyboardShortcuts(e) {
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            this.state.loadStateFromDOM();
            this.state.saveStateToLocalStorage();
            this.ui.showNotification('數據已保存', 'success');
        }
        if (e.key === 'Escape') {
            this.ui.toggleGenerationModal(false);
        }
    }
    
    /**
     * 導出數據
     */
    exportData() {
        try {
            const dataToExport = this.state.getDataForExport();
            const jsonString = JSON.stringify(dataToExport, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            const title = dataToExport['novel-title'] || 'untitled';
            a.href = url;
            a.download = `novel-project-${title}-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            this.ui.showNotification('專案數據已成功導出！', 'success');
        } catch (error) {
            console.error('導出失敗:', error);
            this.ui.showNotification('導出失敗，請檢查控制台', 'error');
        }
    }

    /**
     * 處理文件導入
     */
    handleFileImport(e) {
        const file = e.target.files[0];
        if (!file) return;

        // 文件類型檢查
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            this.ui.showNotification('請選擇JSON格式的文件', 'error');
            e.target.value = '';
            return;
        }

        // 文件大小檢查（限制10MB）
        if (file.size > 10 * 1024 * 1024) {
            this.ui.showNotification('文件過大，請選擇小於10MB的文件', 'error');
            e.target.value = '';
            return;
        }

        const reader = new FileReader();

        reader.onerror = () => {
            this.ui.showNotification('讀取文件時發生錯誤', 'error');
        };

        reader.onload = (event) => {
            try {
                const jsonString = event.target.result;

                // 基本格式檢查
                if (typeof jsonString !== 'string' || jsonString.trim().length === 0) {
                    throw new Error('文件內容為空');
                }

                const data = JSON.parse(jsonString);

                // 數據驗證
                if (typeof data !== 'object' || data === null) {
                    throw new Error('文件格式不正確');
                }

                // 檢查是否是有效的專案數據（可以添加更多驗證）
                if (data.hasOwnProperty('novel-title') || data.hasOwnProperty('characters') || data.hasOwnProperty('outline')) {
                    this.state.loadStateFromImport(data);
                    this.ui.showNotification('專案數據已成功導入！', 'success');
                    this.ui.updateProgress();
                } else {
                    this.ui.showNotification('文件不是有效的專案數據格式', 'error');
                }

            } catch (error) {
                console.error('Import error:', error);
                if (error instanceof SyntaxError) {
                    this.ui.showNotification('文件格式錯誤，請確認是有效的JSON文件', 'error');
                } else {
                    this.ui.showNotification('導入失敗：' + error.message, 'error');
                }
            }
        };

        reader.readAsText(file, 'UTF-8');
        e.target.value = ''; // 允許重複導入同一個文件
    }
}

// 工具類 (可以保持不變或移到單獨的 utils.js)
class Utils {
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                try {
                    func(...args);
                } catch (error) {
                    console.error('Debounced function error:', error);
                }
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 節流函數
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                try {
                    func.apply(this, args);
                } catch (error) {
                    console.error('Throttled function error:', error);
                }
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 安全的DOM查詢
    static safeQuerySelector(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            console.error('Invalid selector:', selector, error);
            return null;
        }
    }

    // 安全的JSON解析
    static safeJSONParse(jsonString, defaultValue = null) {
        try {
            return JSON.parse(jsonString);
        } catch (error) {
            console.error('JSON parse error:', error);
            return defaultValue;
        }
    }
}

// 全局錯誤處理
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    if (window.ui && window.ui.showNotification) {
        window.ui.showNotification('應用程式發生未預期的錯誤，請重新整理頁面', 'error');
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.ui && window.ui.showNotification) {
        window.ui.showNotification('異步操作發生錯誤，請重試', 'error');
    }
});

// 初始化應用程式
document.addEventListener('DOMContentLoaded', () => {
    try {
        const app = new NovelGenerator();
        app.init();
    } catch (error) {
        console.error('Failed to initialize application:', error);
        // 顯示基本錯誤信息
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = 'position:fixed;top:20px;right:20px;background:#ef4444;color:white;padding:16px;border-radius:8px;z-index:9999;max-width:400px;';
        errorDiv.textContent = '應用程式初始化失敗，請重新整理頁面或檢查瀏覽器控制台';
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 10000);
    }
});
