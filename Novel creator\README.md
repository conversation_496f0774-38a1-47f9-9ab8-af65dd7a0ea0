# AI 小說生成器

一個功能豐富的AI小說創作輔助工具，幫助作者構建完整的小說世界觀、角色設定和劇情大綱。

## ✨ 主要功能

### 📝 核心設定
- **小說基礎信息**：書名、類型、寫作風格、故事核心
- **AI輔助填寫**：根據初步想法自動生成基礎設定
- **必填項驗證**：確保關鍵信息完整

### 🌍 世界觀建構
- **分類詳細設定**：地理環境、力量體系、科技文明、社會結構
- **結構化管理**：每個方面都有專門的輸入欄位
- **AI智能生成**：可完善現有設定或全新生成世界觀

### 👥 角色管理
- **多角色支持**：可添加無限個角色
- **詳細屬性**：姓名、定位、外貌、性格、關鍵物品
- **AI角色生成**：一鍵生成完整角色信息
- **折疊式界面**：節省空間，便於管理

### 📖 劇情大綱
- **結構化規劃**：故事整體結構、重要節點、伏筆線索
- **卷章管理**：可視化的卷章編輯器，支持場景和目標設定
- **AI智能建議**：提供創意劇情轉折和發展建議

### 📚 名詞庫
- **分類管理**：人物、地點、物品、功法、組織、術語六大分類
- **詳細記錄**：每個分類都有主要和次要條目
- **一致性保證**：確保設定前後一致，避免矛盾

## 🚀 新增功能

### 🤖 真實AI集成
- **Gemini API支持**：集成Google Gemini AI，提供真實的AI輔助
- **API設定管理**：安全的API Key存儲和連接測試
- **智能內容生成**：根據上下文生成高質量的創作內容
- **降級保護**：API失敗時自動切換到備用模式

### 💾 數據管理
- **自動保存**：實時保存到本地存儲
- **導出功能**：將專案導出為JSON文件
- **導入功能**：從文件恢復專案數據
- **快捷鍵支持**：Ctrl+S保存，Ctrl+Enter快速生成

### 🎨 用戶體驗
- **進度追蹤**：實時顯示完成進度
- **主題切換**：支持深色/淺色主題
- **響應式設計**：完美適配各種設備
- **動畫效果**：流暢的交互動畫

### 🔧 技術優化
- **模組化架構**：代碼結構清晰，易於維護
- **錯誤處理**：完善的錯誤提示和處理
- **性能優化**：快速載入，流暢操作
- **無障礙支持**：良好的可訪問性

## 🎯 使用方法

### 快速開始
1. 打開 `index.html` 文件
2. **設定API Key**：在「核心設定」中輸入您的Gemini API Key
3. 在「核心設定」中填寫基本信息
4. 使用真實AI功能快速生成內容
5. 逐步完善各個模組的詳細設定
6. 查看進度條確認完成度
7. 點擊「總覽與生成」開始創作

### API Key 獲取方法
1. 訪問 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登錄您的Google帳號
3. 創建新的API Key
4. 複製API Key並粘貼到應用中的設定區域
5. 點擊「測試連接」確認API可用

### 快捷鍵
- `Ctrl + S`：保存當前數據
- `Ctrl + Enter`：打開生成模態框
- `Escape`：關閉模態框

### 數據管理
- **導出**：點擊導出按鈕，下載JSON格式的專案文件
- **導入**：點擊導入按鈕，選擇之前導出的JSON文件
- **自動保存**：所有修改都會自動保存到瀏覽器本地存儲

## 📁 專案結構

```
Novel creator/
├── index.html          # 主頁面
├── styles.css          # 樣式文件
├── js/
│   └── app.js          # 主要JavaScript邏輯
└── README.md           # 說明文件
```

## 🛠️ 技術棧

- **前端框架**：原生HTML/CSS/JavaScript
- **UI框架**：Tailwind CSS
- **圖標庫**：Lucide Icons
- **字體**：Noto Sans TC
- **存儲**：localStorage

## 🔮 AI功能說明

目前的AI功能為演示版本，實際使用時可以：

1. **集成真實AI API**：如OpenAI GPT、Claude等
2. **自定義提示詞**：根據不同類型小說調整AI提示
3. **多輪對話**：支持與AI進行多輪創作討論
4. **風格學習**：AI學習用戶的寫作風格

## 📈 進度指示

系統會根據以下標準計算完成進度：

- **核心設定**（25%）：書名和類型為必填
- **世界觀**（20%）：有基本世界設定描述
- **角色設定**（20%）：至少有一個角色
- **劇情大綱**（20%）：有基本劇情框架
- **名詞庫**（15%）：有專有名詞記錄

## 🎨 主題支持

- **深色主題**：適合長時間創作，保護視力
- **淺色主題**：清爽明亮，適合白天使用
- **自動記憶**：系統會記住用戶的主題偏好

## 🔄 更新日誌

### v3.0.0 (最新)
- ✅ **重大更新**：集成真實的Gemini AI API
- ✅ **功能細分**：世界觀、劇情大綱、名詞庫全面重構
- ✅ **卷章管理**：可視化的章節編輯器
- ✅ **API管理**：安全的API Key存儲和測試
- ✅ **智能生成**：基於上下文的AI內容生成
- ✅ **分類管理**：詳細的分類輸入欄位
- ✅ **降級保護**：API失敗時的備用方案

### v2.0.0
- ✅ 完全重構代碼架構
- ✅ 添加進度追蹤功能
- ✅ 實現主題切換
- ✅ 增加導出/導入功能
- ✅ 優化用戶界面和體驗
- ✅ 添加快捷鍵支持
- ✅ 改善響應式設計

### v1.0.0
- ✅ 基礎功能實現
- ✅ 五大模組完成
- ✅ AI模擬功能
- ✅ 本地存儲支持

## 📝 使用建議

1. **循序漸進**：按照標籤頁順序逐步完善設定
2. **善用AI**：充分利用AI輔助功能激發創意
3. **定期備份**：使用導出功能定期備份專案
4. **詳細記錄**：在名詞庫中詳細記錄所有設定
5. **進度監控**：關注進度條，確保各模組均衡發展

## 🤝 貢獻

歡迎提交Issue和Pull Request來改進這個專案！

## 📄 許可證

MIT License - 自由使用和修改
