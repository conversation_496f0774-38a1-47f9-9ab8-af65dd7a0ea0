class UI {
    constructor() {
        this.elements = {
            tabs: document.getElementById('tabs'),
            tabContents: document.getElementById('tab-content').children,
            characterList: document.getElementById('character-list'),
            modal: document.getElementById('generation-modal'),
            generateNovelBtn: document.getElementById('generateNovelBtn'),
            closeModalBtn: document.getElementById('close-modal'),
            container: document.querySelector('.max-w-7xl'),
            themeToggle: document.getElementById('themeToggle'),
            // ... (其他元素可以在這裡添加)
        };
        this.characterCount = 0;
    }

    /**
     * 初始化 UI 組件
     */
    init() {
        this.loadTheme();
        lucide.createIcons();
    }
    
    /**
     * 處理標籤切換
     */
    handleTabSwitch(tabId) {
        // 更新按鈕樣式
        this.elements.tabs.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabId);
        });

        // 顯示/隱藏內容
        Array.from(this.elements.tabContents).forEach(content => {
            const isTargetContent = content.id === `${tabId}-content`;
            content.classList.toggle('hidden', !isTargetContent);
            if (isTargetContent) {
                content.classList.add('fade-in');
            }
        });
    }
    
    /**
     * 創建角色卡片HTML
     */
    createCharacterCardHTML(id, data = {}) {
        return `
        <div class="card p-6 rounded-lg character-card" id="character-${id}">
            <div class="flex justify-between items-center mb-4">
                <div class="collapsible-header flex items-center gap-2 cursor-pointer flex-grow" onclick="window.ui.toggleCharacterCard(this)">
                    <i data-lucide="user-round" class="w-5 h-5 text-sky-400"></i>
                    <h3 class="text-lg font-bold text-white">
                        <span id="char-name-display-${id}">${data.name || '新角色'}</span>
                    </h3>
                    <i data-lucide="chevron-down" class="w-5 h-5 toggle-arrow transition-transform ml-2"></i>
                </div>
                <button class="remove-character text-red-500 hover:text-red-400 transition-colors p-2 rounded hover:bg-red-500/10 ml-4" data-id="${id}" title="刪除角色">
                    <i data-lucide="trash-2" class="w-5 h-5"></i>
                </button>
            </div>
            <div class="collapsible-content mt-4 space-y-4">
                <!-- ... (和 app.js 中一樣的內容) ... -->
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label class="block text-sm font-medium">姓名</label>
                            <button class="ai-generate-field ai-button text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center gap-1" data-field="name" data-character-id="${id}">
                                <i data-lucide="sparkles" class="w-3 h-3"></i>AI
                            </button>
                        </div>
                        <input type="text" data-field="name" class="input-field w-full rounded-md p-2 character-name-input" data-display-id="${id}" value="${data.name || ''}" placeholder="林凡" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">角色定位</label>
                        <input type="text" data-field="role" class="input-field w-full rounded-md p-2" value="${data.role || '主角'}" placeholder="主角, 女主角, 反派, 配角">
                    </div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <label class="block text-sm font-medium">外貌</label>
                        <button class="ai-generate-field ai-button text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center gap-1" data-field="appearance" data-character-id="${id}">
                            <i data-lucide="sparkles" class="w-3 h-3"></i>AI
                        </button>
                    </div>
                    <textarea data-field="appearance" class="input-field w-full rounded-md p-2" rows="2" placeholder="黑髮黑瞳，面容清秀，眼神堅毅。">${data.appearance || ''}</textarea>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <label class="block text-sm font-medium">性格與背景</label>
                        <button class="ai-generate-field ai-button text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center gap-1" data-field="personality" data-character-id="${id}">
                            <i data-lucide="sparkles" class="w-3 h-3"></i>AI
                        </button>
                    </div>
                    <textarea data-field="personality" class="input-field w-full rounded-md p-2" rows="3" placeholder="性格堅毅，重情重義。本是天才，後遭人陷害，丹田被廢，成為廢柴。">${data.personality || ''}</textarea>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <label class="block text-sm font-medium">金手指/關鍵物品</label>
                        <button class="ai-generate-field ai-button text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center gap-1" data-field="item" data-character-id="${id}">
                            <i data-lucide="sparkles" class="w-3 h-3"></i>AI
                        </button>
                    </div>
                    <input type="text" data-field="item" class="input-field w-full rounded-md p-2" value="${data.item || ''}" placeholder="神秘龍戒">
                </div>
                <div class="flex gap-2 mt-4">
                    <button class="ai-fill-character flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button" data-id="${id}">
                        <i data-lucide="wand-2" class="w-5 h-5"></i>AI 輔助完善
                    </button>
                    <button class="ai-generate-character flex-1 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button" data-id="${id}">
                        <i data-lucide="sparkles" class="w-5 h-5"></i>AI 輔助生成
                    </button>
                </div>
            </div>
        </div>`;
    }

    /**
     * 添加角色卡片到UI
     */
    addCharacterCard(data = {}) {
        this.characterCount++;
        const cardHTML = this.createCharacterCardHTML(this.characterCount, data);
        this.elements.characterList.insertAdjacentHTML('beforeend', cardHTML);
        lucide.createIcons();
        
        const newCard = document.getElementById(`character-${this.characterCount}`);
        newCard.style.opacity = '0';
        newCard.style.transform = 'translateY(20px)';
        setTimeout(() => {
            newCard.style.transition = 'all 0.3s ease';
            newCard.style.opacity = '1';
            newCard.style.transform = 'translateY(0)';
        }, 100);

        return newCard;
    }

    /**
     * 從UI移除角色
     */
    removeCharacter(id) {
        if (confirm('確定要刪除這個角色嗎？')) {
            const card = document.getElementById(`character-${id}`);
            if(card) {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '0';
                card.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    card.remove();
                    window.app.state.loadStateFromDOM(); // 更新 state
                    window.app.state.saveStateToLocalStorage();
                }, 300);
            }
        }
    }

    /**
     * 切換角色卡片折疊
     */
    toggleCharacterCard(header) {
        // 找到角色卡片容器
        const card = header.closest('.character-card');
        const content = card.querySelector('.collapsible-content');
        const arrow = header.querySelector('.toggle-arrow');

        if (content && arrow) {
            content.classList.toggle('open');
            arrow.classList.toggle('rotate-180');
        }
    }
    
    /**
     * 更新角色卡片標題
     */
    updateCharacterNameDisplay(id, name) {
        const displayElement = document.getElementById(`char-name-display-${id}`);
        if (displayElement) {
            displayElement.textContent = name || '新角色';
        }
    }

    /**
     * 顯示/隱藏生成模態框
     */
    toggleGenerationModal(show) {
        if (show) {
            this.populateModalSummary();
            this.elements.modal.classList.remove('hidden');
            this.elements.modal.classList.add('modal-backdrop');
            document.body.style.overflow = 'hidden';
        } else {
            this.elements.modal.classList.add('hidden');
            this.elements.modal.classList.remove('modal-backdrop');
            document.body.style.overflow = 'auto';
        }
    }
    
    /**
     * 填充模態框摘要
     */
    populateModalSummary() {
        const state = window.app.state;
        state.loadStateFromDOM(); // 確保數據最新

        const summaryCore = `書名：${state.data['novel-title'] || '未設定'}\n類型：${state.data['novel-genre'] || '未設定'}\n風格：${state.data['writing-style'] || '未設定'}\n核心：${state.data['story-core'] || '未設定'}`;

        document.getElementById('summary-core').textContent = summaryCore;
        document.getElementById('summary-world').textContent = this.getSectionSummary('world-content') || '尚未設定世界觀。';
        document.getElementById('summary-characters').textContent = this.getCharacterSummary(state.data.characters) || '尚未設定任何角色。';
        document.getElementById('summary-outline').textContent = this.getOutlineSummary(state.data.outline) || '尚未設定劇情大綱。';
        document.getElementById('summary-glossary').textContent = this.getSectionSummary('glossary-content') || '尚未設定名詞庫。';
    }

    getSectionSummary(contentId) {
        const section = document.getElementById(contentId);
        if (!section) return '';
        const inputs = section.querySelectorAll('textarea');
        let summary = '';
        inputs.forEach(input => {
            if (input.value.trim()) {
                const label = input.previousElementSibling;
                if (label) {
                    summary += `${label.textContent.trim()}：\n${input.value.trim()}\n\n`;
                }
            }
        });
        return summary.trim();
    }

    getCharacterSummary(characters) {
        if (!characters || characters.length === 0) return '尚未設定任何角色。';
        return characters.map(c => `${c.role || '未設定'}：${c.name || '未命名'}`).join('\n');
    }

    getOutlineSummary(outline) {
        if (!outline || outline.length === 0) return '尚未設定劇情大綱。';

        let summary = '';
        outline.forEach((volume, volIndex) => {
            const volumeTitle = volume.title || `第 ${volIndex + 1} 卷`;
            summary += `${volumeTitle}\n`;

            if (volume.description && volume.description.trim()) {
                summary += `  描述：${volume.description.trim()}\n`;
            }

            if (volume.chapters && volume.chapters.length > 0) {
                volume.chapters.forEach((chapter, chapIndex) => {
                    const chapterTitle = chapter.title || `第 ${chapIndex + 1} 章`;
                    summary += `  ${chapterTitle}`;

                    if (chapter.scene && chapter.scene.trim()) {
                        summary += ` - 場景：${chapter.scene.trim()}`;
                    }
                    if (chapter.goal && chapter.goal.trim()) {
                        summary += ` - 目標：${chapter.goal.trim()}`;
                    }
                    summary += '\n';
                });
            } else {
                summary += '  (尚無章節)\n';
            }
            summary += '\n';
        });

        return summary.trim();
    }

    /**
     * 渲染整個大綱結構
     */
    renderOutline(volumes = []) {
        const container = document.getElementById('volumes-container');
        container.innerHTML = '';
        volumes.forEach(volumeData => {
            const volumeEl = this.addVolume(volumeData);
            if (volumeData.chapters && volumeData.chapters.length > 0) {
                volumeData.chapters.forEach(chapterData => {
                    this.addChapter(volumeEl, chapterData);
                });
            }
        });
        this.initSortable();
    }

    /**
     * 新增卷到 UI
     */
    addVolume(data = {}) {
        const container = document.getElementById('volumes-container');
        const id = data.id || `vol-${Date.now()}`;
        const title = data.title || `第 ${container.children.length + 1} 卷：新篇章`;
        const description = data.description || '';

        const element = document.createElement('div');
        element.className = 'volume-card card p-4 rounded-lg';
        element.dataset.id = id;
        element.innerHTML = this.createVolumeHTML(id, title, description);
        
        container.appendChild(element);
        lucide.createIcons();
        return element;
    }

    /**
     * 新增章節到 UI
     */
    addChapter(volumeEl, data = {}) {
        const container = volumeEl.querySelector('.chapters-container');
        const id = data.id || `ch-${Date.now()}`;
        const title = data.title || `第 ${container.children.length + 1} 章：新的開始`;
        const scene = data.scene || '';
        const goal = data.goal || '';

        const element = document.createElement('div');
        element.className = 'chapter-card bg-gray-700 p-3 rounded';
        element.dataset.id = id;
        element.innerHTML = this.createChapterHTML(id, title, scene, goal);
        
        container.appendChild(element);
        lucide.createIcons();
        return element;
    }

    createVolumeHTML(id, title, description) {
        return `
            <div class="flex justify-between items-center mb-3">
                <div class="flex items-center gap-2 flex-grow">
                    <i data-lucide="grip-vertical" class="w-5 h-5 text-gray-500 cursor-move volume-handle"></i>
                    <i data-lucide="book" class="w-5 h-5 text-purple-400"></i>
                    <input type="text" class="volume-title bg-transparent border-none text-white font-bold text-lg focus:outline-none focus:bg-gray-700 rounded px-2 w-full" value="${title}">
                </div>
                <div class="flex gap-2">
                    <button class="ai-generate-volume bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm flex items-center gap-1 ai-button" data-volume-id="${id}">
                        <i data-lucide="sparkles" class="w-3 h-3"></i>AI生成
                    </button>
                    <button class="add-chapter bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center gap-1" data-volume-id="${id}">
                        <i data-lucide="plus" class="w-3 h-3"></i>章節
                    </button>
                    <button class="remove-volume bg-red-600 hover:bg-red-700 text-white p-2 rounded text-sm" data-id="${id}">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
            <div class="volume-description ml-7">
                <textarea class="input-field w-full rounded-md p-2 text-sm" rows="2" placeholder="卷的簡介和主要內容...">${description}</textarea>
            </div>
            <div class="chapters-container space-y-2 mt-3 ml-7"></div>
        `;
    }

    createChapterHTML(id, title, scene, goal) {
        return `
            <div class="flex items-start gap-2">
                <i data-lucide="grip-vertical" class="w-5 h-5 text-gray-500 cursor-move chapter-handle mt-2"></i>
                <div class="flex-grow">
                    <div class="flex justify-between items-center mb-2">
                        <input type="text" class="chapter-title bg-transparent border-none text-white font-semibold focus:outline-none focus:bg-gray-600 rounded px-2 w-full" value="${title}">
                        <button class="remove-chapter text-red-400 hover:text-red-300 ml-2" data-id="${id}">
                            <i data-lucide="x" class="w-4 h-4"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <textarea data-field="scene" class="input-field w-full rounded p-2 text-xs" rows="2" placeholder="場景設定...">${scene}</textarea>
                        <textarea data-field="goal" class="input-field w-full rounded p-2 text-xs" rows="2" placeholder="章節目標...">${goal}</textarea>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化拖曳排序功能
     */
    initSortable() {
        const volumesContainer = document.getElementById('volumes-container');
        new Sortable(volumesContainer, {
            animation: 150,
            handle: '.volume-handle',
            ghostClass: 'sortable-ghost',
            onEnd: () => window.app.state.loadStateFromDOM() // 拖曳結束後更新 state
        });

        document.querySelectorAll('.chapters-container').forEach(container => {
            new Sortable(container, {
                animation: 150,
                handle: '.chapter-handle',
                ghostClass: 'sortable-ghost',
                onEnd: () => window.app.state.loadStateFromDOM()
            });
        });
    }

    /**
     * 顯示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-green-600 text-white' :
            type === 'error' ? 'bg-red-600 text-white' :
            'bg-blue-600 text-white'
        }`;
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        notification.textContent = message;

        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    /**
     * 主題切換
     */
    toggleTheme() {
        const isLight = document.body.classList.toggle('light-theme');
        const icon = this.elements.themeToggle.querySelector('i');
        icon.setAttribute('data-lucide', isLight ? 'sun' : 'moon');
        lucide.createIcons();
        localStorage.setItem('theme', isLight ? 'light' : 'dark');
        this.showNotification(`已切換到 ${isLight ? '淺色' : '深色'} 主題`, 'success');
    }

    /**
     * 載入主題
     */
    loadTheme() {
        if (localStorage.getItem('theme') === 'light') {
            document.body.classList.add('light-theme');
            const icon = this.elements.themeToggle.querySelector('i');
            icon.setAttribute('data-lucide', 'sun');
        }
    }
    
    /**
     * 更新進度條
     */
    updateProgress() {
        const state = window.app.state;
        const sections = [
             { id: 'core', fields: ['novel-title', 'novel-genre'], weight: 25 },
             { id: 'world', fields: ['world-geography', 'power-system'], weight: 20 },
             { id: 'characters', custom: () => state.data.characters && state.data.characters.length > 0, weight: 20 },
             { id: 'outline', custom: () => state.data.outline && state.data.outline.length > 0 && state.data.outline[0].chapters.length > 0, weight: 20 },
             { id: 'glossary', fields: ['characters-main', 'locations-major'], weight: 15 }
        ];

        let totalProgress = 0;
        sections.forEach(section => {
            let sectionComplete = section.custom ? section.custom() : section.fields.every(fieldId => state.data[fieldId] && state.data[fieldId].trim() !== '');
            if (sectionComplete) {
                totalProgress += section.weight;
            }
            const indicator = document.getElementById(`${section.id}-indicator`);
            if (indicator) {
                indicator.className = `w-2 h-2 rounded-full ml-1 transition-colors ${sectionComplete ? 'bg-green-400' : 'bg-gray-500'}`;
            }
        });

        document.getElementById('progress-bar').style.width = `${totalProgress}%`;
        document.getElementById('progress-text').textContent = `${totalProgress}%`;
    }

    // 其他 UI 相關輔助函數...
}
