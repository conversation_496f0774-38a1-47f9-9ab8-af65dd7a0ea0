@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700&display=swap');

/* Base Styles */
body {
    font-family: 'Noto Sans TC', sans-serif;
    background-color: #111827;
    color: #d1d5db;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light Theme */
body.light-theme {
    background-color: #f8fafc;
    color: #1e293b;
}

body.light-theme .card {
    background-color: #ffffff;
    border-color: #e2e8f0;
}

body.light-theme .input-field {
    background-color: #ffffff;
    border-color: #d1d5db;
    color: #1e293b;
    resize: vertical;
    overflow-y: auto;
    min-height: 2.5rem;
    max-height: 300px;
}

body.light-theme textarea.input-field {
    min-height: 4rem;
    max-height: 400px;
}

body.light-theme .input-field:focus {
    border-color: #38bdf8;
    background-color: #ffffff;
}

/* 淺色主題的捲動條樣式 */
body.light-theme .input-field::-webkit-scrollbar {
    width: 8px;
}

body.light-theme .input-field::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

body.light-theme .input-field::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

body.light-theme .input-field::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

body.light-theme .section-title {
    border-bottom-color: #e2e8f0;
}

body.light-theme .collapsible-header:hover {
    background-color: rgba(226, 232, 240, 0.3);
}

/* Tab Styles */
.tab-button {
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-button.active {
    color: #38bdf8;
    border-bottom-color: #38bdf8;
}

.tab-button:hover {
    color: #60a5fa;
}

/* Button Styles */
.ai-button {
    transition: all 0.3s ease;
}

.ai-button:hover {
    box-shadow: 0 0 15px rgba(56, 189, 248, 0.4);
    transform: translateY(-1px);
}

.ai-button:active {
    transform: translateY(0);
}

/* Card Styles */
.card {
    background-color: #1f2937;
    border: 1px solid #374151;
    transition: border-color 0.3s ease;
}

.card:hover {
    border-color: #4b5563;
}

/* Input Styles */
.input-field {
    background-color: #374151;
    border: 1px solid #4b5563;
    color: #d1d5db;
    transition: all 0.3s ease;
    resize: vertical; /* 允許垂直調整大小 */
    overflow-y: auto; /* 確保可以捲動 */
    min-height: 2.5rem; /* 最小高度 */
    max-height: 300px; /* 最大高度，超過會出現捲動條 */
}

/* 針對textarea的特殊樣式 */
textarea.input-field {
    resize: vertical;
    overflow-y: auto;
    min-height: 4rem;
    max-height: 400px;
    line-height: 1.5;
    padding: 0.75rem;
}

/* 針對單行input的樣式 */
input.input-field {
    overflow-x: auto;
    white-space: nowrap;
}

.input-field:focus {
    outline: none;
    border-color: #38bdf8;
    box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3);
    background-color: #4b5563;
}

.input-field::placeholder {
    color: #9ca3af;
}

/* 確保捲動條樣式 */
.input-field::-webkit-scrollbar {
    width: 8px;
}

.input-field::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

.input-field::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 4px;
}

.input-field::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Section Styles */
.section-title {
    border-bottom: 1px solid #374151;
    padding-bottom: 8px;
    margin-bottom: 16px;
}

/* Collapsible Styles */
.collapsible-header {
    cursor: pointer;
    transition: all 0.3s ease;
}

.collapsible-header:hover {
    background-color: rgba(55, 65, 81, 0.3);
    border-radius: 0.5rem;
    padding: 0.5rem;
    margin: -0.5rem;
}

.collapsible-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
}

.collapsible-content.open {
    max-height: 2000px;
}

/* Animation Styles */
.toggle-arrow {
    transition: transform 0.3s ease;
}

.rotate-180 {
    transform: rotate(180deg);
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Slide In Animation */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.bounce {
    animation: bounce 1s ease;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Modal Styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* Responsive Improvements */
@media (max-width: 640px) {
    .tab-button {
        font-size: 0.875rem;
        padding: 0.75rem 0.5rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .collapsible-header:hover {
        background-color: transparent;
        padding: 0;
        margin: 0;
    }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #38bdf8, #818cf8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-glow {
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
}

/* Error and Success States */
.error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
}

.success {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
}

.error-text {
    color: #fca5a5;
}

.success-text {
    color: #6ee7b7;
}
