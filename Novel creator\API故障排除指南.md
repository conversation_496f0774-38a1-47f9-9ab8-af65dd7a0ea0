# API 故障排除指南

## 🔧 我已經為您添加了強大的診斷工具

### 新增功能：
1. **詳細調試日誌** - 顯示每一步的執行過程
2. **全面診斷工具** - 一鍵檢測所有可能的問題
3. **智能錯誤分析** - 提供具體的解決方案

## 🚀 使用新的診斷功能

### 步驟1：打開調試面板
1. 進入「核心設定」標籤
2. 在API設定區域，點擊「顯示調試信息」
3. 調試面板會顯示在下方

### 步驟2：運行全面診斷
1. 輸入您的API Key
2. 點擊「診斷」按鈕（紫色按鈕）
3. 系統會自動執行5個步驟的全面檢查

### 步驟3：查看診斷結果
診斷會檢查：
- ✅ API Key 格式是否正確
- ✅ 網絡連接是否正常
- ✅ 模型列表端點是否可用
- ✅ 生成端點是否正常工作
- ✅ 常見問題提醒

## 🔍 常見問題及解決方案

### 問題1：API Key 格式錯誤
**症狀：** 顯示「API Key 格式不正確」
**解決：**
- 確認API Key以「AIza」開頭
- 檢查是否完整複製（通常39個字符）
- 重新從Google AI Studio獲取

### 問題2：403 權限錯誤
**症狀：** 顯示「權限不足或服務未啟用」
**解決：**
1. 訪問 [Google Cloud Console](https://console.cloud.google.com/)
2. 確認已啟用「Generative Language API」
3. 檢查API Key的項目設定
4. 確認計費帳戶已設定（如需要）

### 問題3：404 端點不存在
**症狀：** 顯示「API 端點不存在」
**解決：**
- 確認使用的是Gemini API Key（不是其他Google API）
- 檢查API Key是否來自正確的項目
- 嘗試重新生成API Key

### 問題4：429 配額超限
**症狀：** 顯示「調用頻率過高」
**解決：**
- 等待一段時間後重試
- 檢查API配額設定
- 考慮升級API計劃

### 問題5：網絡連接問題
**症狀：** 顯示「網絡連接失敗」
**解決：**
- 檢查網絡連接
- 確認防火牆設定
- 嘗試使用VPN（如果在某些地區）

## 📋 API Key 獲取步驟（詳細版）

### 方法1：Google AI Studio（推薦）
1. 訪問 https://makersuite.google.com/app/apikey
2. 登錄Google帳號
3. 點擊「Create API Key」
4. 選擇項目或創建新項目
5. 複製生成的API Key

### 方法2：Google Cloud Console
1. 訪問 https://console.cloud.google.com/
2. 創建或選擇項目
3. 啟用「Generative Language API」
4. 前往「憑證」頁面
5. 創建「API金鑰」
6. 複製生成的API Key

## 🛠️ 高級故障排除

### 檢查瀏覽器控制台
1. 按F12打開開發者工具
2. 查看Console標籤的錯誤信息
3. 查看Network標籤的請求詳情

### 測試API Key的其他方法
使用curl命令測試（在命令行中）：
```bash
curl -H 'Content-Type: application/json' \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     -X POST 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=YOUR_API_KEY'
```

### 檢查API服務狀態
訪問 [Google Cloud Status](https://status.cloud.google.com/) 檢查服務是否正常

## 🎯 使用診斷結果

### 如果所有測試都通過
- ✅ API設定正確，可以正常使用AI功能

### 如果模型列表失敗但生成成功
- ⚠️ 可能是權限問題，但基本功能可用

### 如果生成失敗
- ❌ 需要檢查API Key權限和服務啟用狀態

### 如果網絡測試失敗
- ❌ 檢查網絡連接和防火牆設定

## 📞 獲取幫助

如果診斷後仍然無法解決問題：

1. **記錄診斷信息**：複製調試面板中的所有信息
2. **檢查錯誤代碼**：記錄具體的HTTP狀態碼
3. **確認環境**：記錄瀏覽器版本和操作系統
4. **API Key來源**：確認是從哪個平台獲取的API Key

## 💡 最佳實踐

1. **定期測試**：API Key可能會過期或被撤銷
2. **監控配額**：注意API使用量避免超限
3. **安全存儲**：不要在公開場所分享API Key
4. **備用方案**：準備多個API Key以防萬一

現在請使用新的診斷功能來檢查您的API設定！
