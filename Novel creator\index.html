<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI小說生成器 - 專業的小說創作輔助工具，幫助作者構建完整的世界觀、角色設定和劇情大綱">
    <meta name="keywords" content="AI小說生成器,小說創作,世界觀建構,角色設定,劇情大綱">
    <meta name="author" content="Novel Creator Team">
    <title>AI 小說生成器 - 世界觀建構</title>

    <!-- External Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body class="p-4 sm:p-6 md:p-8">

    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <div class="flex items-center gap-3 mb-4 sm:mb-0">
                <i data-lucide="book-marked" class="w-10 h-10 text-sky-400"></i>
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gradient">AI 小說生成器</h1>
                    <p class="text-sm text-gray-400">打造你的專屬世界，讓 AI 為你譜寫傳奇</p>
                    <p class="text-xs text-gray-500 mt-1">快捷鍵：Ctrl+S 保存 | Ctrl+Enter 快速生成</p>
                </div>
            </div>
            <div class="flex gap-2 w-full sm:w-auto">
                <button id="themeToggle" class="flex-1 sm:flex-none bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-3 rounded-lg flex items-center justify-center" title="切換主題">
                    <i data-lucide="moon" class="w-5 h-5"></i>
                </button>
                <div class="relative">
                    <input type="file" id="importFile" accept=".json" class="hidden">
                    <button id="importBtn" class="flex-1 sm:flex-none bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2">
                        <i data-lucide="upload" class="w-5 h-5"></i>
                        <span class="hidden sm:inline">導入</span>
                    </button>
                </div>
                <button id="exportBtn" class="flex-1 sm:flex-none bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2">
                    <i data-lucide="download" class="w-5 h-5"></i>
                    <span class="hidden sm:inline">導出</span>
                </button>
                <button id="generateNovelBtn" class="flex-1 sm:flex-none bg-sky-500 hover:bg-sky-600 text-white font-bold py-2 px-6 rounded-lg flex items-center justify-center gap-2 shadow-glow">
                    <i data-lucide="sparkles" class="w-5 h-5"></i>
                    <span>總覽與生成</span>
                </button>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-400">完成進度</span>
                <span id="progress-text" class="text-sm text-sky-400">0%</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
                <div id="progress-bar" class="bg-gradient-to-r from-sky-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6 border-b border-gray-700">
            <nav class="flex flex-wrap -mb-px" id="tabs">
                <button data-tab="core" class="tab-button active text-base font-medium py-3 px-4 flex items-center gap-2">
                    <i data-lucide="settings-2" class="w-5 h-5"></i>核心設定
                    <span id="core-indicator" class="w-2 h-2 bg-gray-500 rounded-full ml-1"></span>
                </button>
                <button data-tab="world" class="tab-button text-base font-medium py-3 px-4 flex items-center gap-2">
                    <i data-lucide="globe-2" class="w-5 h-5"></i>世界觀
                    <span id="world-indicator" class="w-2 h-2 bg-gray-500 rounded-full ml-1"></span>
                </button>
                <button data-tab="characters" class="tab-button text-base font-medium py-3 px-4 flex items-center gap-2">
                    <i data-lucide="users" class="w-5 h-5"></i>角色設定
                    <span id="characters-indicator" class="w-2 h-2 bg-gray-500 rounded-full ml-1"></span>
                </button>
                <button data-tab="outline" class="tab-button text-base font-medium py-3 px-4 flex items-center gap-2">
                    <i data-lucide="list-tree" class="w-5 h-5"></i>劇情大綱
                    <span id="outline-indicator" class="w-2 h-2 bg-gray-500 rounded-full ml-1"></span>
                </button>
                <button data-tab="glossary" class="tab-button text-base font-medium py-3 px-4 flex items-center gap-2">
                    <i data-lucide="book-key" class="w-5 h-5"></i>名詞庫
                    <span id="glossary-indicator" class="w-2 h-2 bg-gray-500 rounded-full ml-1"></span>
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div id="tab-content">
            <!-- Core Settings Tab -->
            <div id="core-content" class="tab-panel grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2"><i data-lucide="book-open" class="w-5 h-5 text-sky-400"></i>作品基礎</h3>
                    <div class="space-y-4">
                        <div>
                            <label for="novel-title" class="block text-sm font-medium mb-1">
                                小說名稱 <span class="text-red-400">*</span>
                            </label>
                            <input type="text" id="novel-title" class="input-field w-full rounded-md p-2" placeholder="例如：星塵主宰" required>
                        </div>
                        <div>
                            <label for="novel-genre" class="block text-sm font-medium mb-1">
                                故事類型 <span class="text-red-400">*</span>
                                <span class="text-xs text-gray-500">(可複選)</span>
                            </label>
                            <input type="text" id="novel-genre" class="input-field w-full rounded-md p-2" placeholder="玄幻, 穿越, 系統流, 無敵" required>
                        </div>
                        <div>
                            <label for="writing-style" class="block text-sm font-medium mb-1">寫作風格</label>
                            <input type="text" id="writing-style" class="input-field w-full rounded-md p-2" placeholder="輕鬆詼諧, 殺伐果斷">
                        </div>
                        <div>
                            <label for="story-core" class="block text-sm font-medium mb-1">故事核心</label>
                            <input type="text" id="story-core" class="input-field w-full rounded-md p-2" placeholder="廢柴逆襲, 復仇, 守護家人">
                        </div>
                    </div>
                </div>
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2"><i data-lucide="bot" class="w-5 h-5 text-sky-400"></i>AI 輔助設定</h3>
                    <div class="space-y-4">
                        <!-- API 設定區域 -->
                        <div class="bg-gray-800 p-4 rounded-lg border border-gray-600">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="text-lg font-semibold text-white flex items-center gap-2">
                                    <i data-lucide="key" class="w-4 h-4 text-yellow-400"></i>
                                    API 設定
                                </h4>
                                <div class="flex items-center gap-2">
                                    <div id="api-status" class="w-3 h-3 bg-red-500 rounded-full" title="API 未連接"></div>
                                    <span id="api-status-text" class="text-sm text-gray-400">未連接</span>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div>
                                    <label for="gemini-api-key" class="block text-sm font-medium mb-1">
                                        Gemini API Key
                                        <span class="text-xs text-gray-500">(安全存儲在本地)</span>
                                    </label>
                                    <div class="flex gap-2">
                                        <input type="password" id="gemini-api-key" class="input-field flex-1 rounded-md p-2" placeholder="輸入您的 Gemini API Key">
                                        <button id="toggle-api-key" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md" title="顯示/隱藏 API Key">
                                            <i data-lucide="eye" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex gap-2 flex-wrap">
                                    <button id="test-api" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2">
                                        <i data-lucide="wifi" class="w-4 h-4"></i>測試連接
                                    </button>
                                    <button id="test-endpoints" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-3 rounded-lg flex items-center gap-2 text-sm">
                                        <i data-lucide="search" class="w-4 h-4"></i>診斷
                                    </button>
                                    <button id="save-api" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2">
                                        <i data-lucide="save" class="w-4 h-4"></i>保存設定
                                    </button>
                                </div>
                                <div class="text-xs text-gray-500">
                                    <p>💡 如何獲取 API Key：</p>
                                    <p>1. 訪問 <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-blue-400 hover:text-blue-300">Google AI Studio</a></p>
                                    <p>2. 創建新的 API Key</p>
                                    <p>3. 複製並粘貼到上方輸入框</p>
                                    <p>4. API Key 應該以 "AIza" 開頭</p>
                                </div>

                                <!-- 調試信息面板 -->
                                <div id="debug-panel" class="mt-3 p-3 bg-gray-900 rounded border border-gray-600 hidden">
                                    <div class="flex justify-between items-center mb-2">
                                        <h5 class="text-sm font-semibold text-yellow-400">🔍 調試信息</h5>
                                        <button id="clear-debug" class="text-xs text-gray-400 hover:text-white">清除</button>
                                    </div>
                                    <div id="debug-content" class="text-xs text-gray-300 font-mono max-h-32 overflow-y-auto"></div>
                                </div>

                                <button id="toggle-debug" class="mt-2 text-xs text-gray-400 hover:text-blue-400 underline">
                                    顯示調試信息
                                </button>
                            </div>
                        </div>

                        <div>
                            <div class="flex justify-between items-center mb-1">
                                <label for="ai-idea" class="block text-sm font-medium">告訴 AI 你的初步想法</label>
                                <button id="ai-generate-idea" class="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded flex items-center gap-1 ai-button">
                                    <i data-lucide="sparkles" class="w-3 h-3"></i>AI 生成想法
                                </button>
                            </div>
                            <textarea id="ai-idea" class="input-field w-full rounded-md p-2" rows="4" placeholder="我想寫一個主角在未來賽博龐克世界，意外撿到一本修仙秘籍的故事..."></textarea>
                        </div>
                        <div class="flex gap-4">
                            <button id="ai-enhance-core" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                                <i data-lucide="wand-2" class="w-5 h-5"></i>
                                AI 輔助完善
                            </button>
                            <button id="ai-generate-core" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                                <i data-lucide="sparkles" class="w-5 h-5"></i>
                                AI 輔助生成
                            </button>
                        </div>
                        <div id="ai-core-output" class="text-sm text-gray-400 p-3 bg-gray-800 rounded-md hidden"></div>
                    </div>
                </div>
            </div>

            <!-- World Settings Tab -->
            <div id="world-content" class="tab-panel hidden space-y-6">
                <!-- 地理環境 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="mountain-snow" class="w-5 h-5 text-sky-400"></i>地理環境
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="world-geography" class="block text-sm font-medium mb-1">大陸與地形</label>
                            <textarea id="world-geography" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：蒼玄大陸，分為東荒、西漠、南嶺、北原、中州五大域..."></textarea>
                        </div>
                        <div>
                            <label for="world-climate" class="block text-sm font-medium mb-1">氣候與環境</label>
                            <textarea id="world-climate" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：四季分明，靈氣濃郁的地方常年春暖花開..."></textarea>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="world-locations" class="block text-sm font-medium mb-1">重要地點</label>
                        <textarea id="world-locations" class="input-field w-full rounded-md p-2" rows="3" placeholder="例如：天劍宗（東荒第一大宗）、萬妖森林（危險禁地）、中州帝都..."></textarea>
                    </div>
                </div>

                <!-- 力量體系 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="zap" class="w-5 h-5 text-sky-400"></i>力量體系
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="power-system" class="block text-sm font-medium mb-1">修煉體系</label>
                            <textarea id="power-system" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：煉氣→築基→金丹→元嬰→化神→合體→大乘→渡劫..."></textarea>
                        </div>
                        <div>
                            <label for="power-source" class="block text-sm font-medium mb-1">力量來源</label>
                            <textarea id="power-source" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：天地靈氣、血脈之力、功法修煉、天材地寶..."></textarea>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="power-rules" class="block text-sm font-medium mb-1">修煉規則</label>
                        <textarea id="power-rules" class="input-field w-full rounded-md p-2" rows="3" placeholder="例如：突破需要渡劫、不同體質修煉速度不同、功法品級影響上限..."></textarea>
                    </div>
                </div>

                <!-- 科技與文明 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="cpu" class="w-5 h-5 text-sky-400"></i>科技與文明
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="tech-level" class="block text-sm font-medium mb-1">科技水平</label>
                            <textarea id="tech-level" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：修仙文明與機關術並存，有飛舟、傳送陣、煉丹爐..."></textarea>
                        </div>
                        <div>
                            <label for="civilization" class="block text-sm font-medium mb-1">文明發展</label>
                            <textarea id="civilization" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：以宗門為主的修仙文明，重視血脈傳承..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- 社會結構 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="users" class="w-5 h-5 text-sky-400"></i>社會結構
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="social-structure" class="block text-sm font-medium mb-1">階級制度</label>
                            <textarea id="social-structure" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：修仙者地位崇高，凡人為底層，宗門長老權力極大..."></textarea>
                        </div>
                        <div>
                            <label for="organizations" class="block text-sm font-medium mb-1">重要勢力</label>
                            <textarea id="organizations" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：天劍宗、萬毒教、商盟、皇室..."></textarea>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="world-rules" class="block text-sm font-medium mb-1">世界法則</label>
                        <textarea id="world-rules" class="input-field w-full rounded-md p-2" rows="3" placeholder="例如：強者為尊、弱肉強食、天道輪迴、因果報應..."></textarea>
                    </div>
                </div>

                <!-- AI 輔助按鈕 -->
                <div class="flex gap-4">
                    <button id="ai-enhance-world" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                        <i data-lucide="wand-2" class="w-5 h-5"></i>AI 輔助完善
                    </button>
                    <button id="ai-generate-world" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                        <i data-lucide="sparkles" class="w-5 h-5"></i>AI 輔助生成
                    </button>
                </div>
            </div>

            <!-- Characters Tab -->
            <div id="characters-content" class="tab-panel hidden space-y-6">
                <div id="character-list" class="space-y-4">
                    <!-- Character cards will be injected here -->
                </div>
                <button id="add-character" class="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2">
                    <i data-lucide="user-plus" class="w-5 h-5"></i>新增角色
                </button>
            </div>

            <!-- Outline Tab -->
            <div id="outline-content" class="tab-panel hidden space-y-6">
                <!-- 卷章結構管理 -->
                <div class="card p-6 rounded-lg">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-white flex items-center gap-2">
                            <i data-lucide="list-tree" class="w-5 h-5 text-sky-400"></i>卷章結構
                        </h3>
                        <button id="add-volume" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2">
                            <i data-lucide="plus" class="w-4 h-4"></i>新增卷
                        </button>
                    </div>
                    <p class="text-sm text-gray-400 mb-4">在這裡規劃您的故事結構。您可以新增不同的「卷」，並在其中添加章節。試著拖曳它們來重新排序！</p>
                    <div id="volumes-container" class="space-y-4">
                        <!-- 卷章卡片將會被動態插入到這裡 -->
                    </div>
                </div>

                 <!-- AI 輔助按鈕 -->
                <div class="flex gap-4 mt-6">
                    <button id="ai-enhance-outline" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                        <i data-lucide="wand-2" class="w-5 h-5"></i>AI 輔助完善
                    </button>
                    <button id="ai-generate-outline" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 ai-button">
                        <i data-lucide="sparkles" class="w-5 h-5"></i>AI 輔助生成
                    </button>
                </div>
            </div>

            <!-- Glossary Tab -->
            <div id="glossary-content" class="tab-panel hidden space-y-6">
                <!-- 人物名錄 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="user" class="w-5 h-5 text-sky-400"></i>人物名錄
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="characters-main" class="block text-sm font-medium mb-1">主要人物</label>
                            <textarea id="characters-main" class="input-field w-full rounded-md p-2" rows="5" placeholder="例如：&#10;林凡 - 主角，廢柴逆襲型&#10;蘇雨薇 - 女主角，天才少女&#10;林天霸 - 反派，林家大少爺"></textarea>
                        </div>
                        <div>
                            <label for="characters-supporting" class="block text-sm font-medium mb-1">配角與路人</label>
                            <textarea id="characters-supporting" class="input-field w-full rounded-md p-2" rows="5" placeholder="例如：&#10;王老頭 - 雜貨店老闆&#10;李長老 - 宗門長老&#10;小二 - 客棧夥計"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 地點名錄 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="map" class="w-5 h-5 text-sky-400"></i>地點名錄
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="locations-major" class="block text-sm font-medium mb-1">重要地點</label>
                            <textarea id="locations-major" class="input-field w-full rounded-md p-2" rows="5" placeholder="例如：&#10;天劍宗 - 東荒第一大宗&#10;萬妖森林 - 危險禁地&#10;中州帝都 - 政治中心"></textarea>
                        </div>
                        <div>
                            <label for="locations-minor" class="block text-sm font-medium mb-1">次要地點</label>
                            <textarea id="locations-minor" class="input-field w-full rounded-md p-2" rows="5" placeholder="例如：&#10;青石鎮 - 主角家鄉&#10;醉仙樓 - 著名酒樓&#10;黑市 - 地下交易場所"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 物品道具 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="package" class="w-5 h-5 text-sky-400"></i>物品道具
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="items-weapons" class="block text-sm font-medium mb-1">武器法寶</label>
                            <textarea id="items-weapons" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;神秘龍戒 - 主角金手指&#10;斬妖劍 - 宗門重寶&#10;護身符 - 防禦法寶"></textarea>
                        </div>
                        <div>
                            <label for="items-consumables" class="block text-sm font-medium mb-1">丹藥材料</label>
                            <textarea id="items-consumables" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;築基丹 - 突破必需&#10;千年人參 - 珍貴藥材&#10;靈石 - 修煉貨幣"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 功法技能 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="scroll" class="w-5 h-5 text-sky-400"></i>功法技能
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="skills-cultivation" class="block text-sm font-medium mb-1">修煉功法</label>
                            <textarea id="skills-cultivation" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;《大荒囚天指》- 主角絕學&#10;《天劍訣》- 宗門心法&#10;《九轉金身》- 體修功法"></textarea>
                        </div>
                        <div>
                            <label for="skills-combat" class="block text-sm font-medium mb-1">戰鬥技能</label>
                            <textarea id="skills-combat" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;劍氣縱橫 - 劍修絕技&#10;雷霆萬鈞 - 雷系法術&#10;瞬移術 - 身法類技能"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 組織勢力 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="shield" class="w-5 h-5 text-sky-400"></i>組織勢力
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="organizations-righteous" class="block text-sm font-medium mb-1">正道勢力</label>
                            <textarea id="organizations-righteous" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;天劍宗 - 劍修聖地&#10;丹王谷 - 煉丹師聚集地&#10;正道聯盟 - 正義組織"></textarea>
                        </div>
                        <div>
                            <label for="organizations-evil" class="block text-sm font-medium mb-1">邪道勢力</label>
                            <textarea id="organizations-evil" class="input-field w-full rounded-md p-2" rows="4" placeholder="例如：&#10;萬毒教 - 用毒高手&#10;血魔宗 - 魔道宗門&#10;暗影組織 - 神秘殺手集團"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 專有術語 -->
                <div class="card p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4 text-white section-title flex items-center gap-2">
                        <i data-lucide="book-key" class="w-5 h-5 text-sky-400"></i>專有術語
                    </h3>
                    <div class="space-y-4">
                        <div>
                            <label for="terms-cultivation" class="block text-sm font-medium mb-1">修煉術語</label>
                            <textarea id="terms-cultivation" class="input-field w-full rounded-md p-2" rows="3" placeholder="例如：渡劫、突破、瓶頸、靈根、丹田..."></textarea>
                        </div>
                        <div>
                            <label for="terms-world" class="block text-sm font-medium mb-1">世界術語</label>
                            <textarea id="terms-world" class="input-field w-full rounded-md p-2" rows="3" placeholder="例如：靈氣、天道、因果、輪迴、天劫..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Final Generation -->
    <div id="generation-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 hidden z-50">
        <div class="card rounded-lg max-w-3xl w-full max-h-[90vh] flex flex-col">
            <div class="p-6 border-b border-gray-700 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-white">設定總覽與生成</h2>
                <button id="close-modal" class="text-gray-400 hover:text-white">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto space-y-6">
                <div>
                    <h3 class="font-bold text-sky-400 mb-2">核心設定</h3>
                    <pre id="summary-core" class="text-sm bg-gray-800 p-3 rounded-md whitespace-pre-wrap"></pre>
                </div>
                <div>
                    <h3 class="font-bold text-sky-400 mb-2">世界觀</h3>
                    <pre id="summary-world" class="text-sm bg-gray-800 p-3 rounded-md whitespace-pre-wrap"></pre>
                </div>
                <div>
                    <h3 class="font-bold text-sky-400 mb-2">主要角色</h3>
                    <pre id="summary-characters" class="text-sm bg-gray-800 p-3 rounded-md whitespace-pre-wrap"></pre>
                </div>
                 <div>
                    <h3 class="font-bold text-sky-400 mb-2">劇情大綱</h3>
                    <pre id="summary-outline" class="text-sm bg-gray-800 p-3 rounded-md whitespace-pre-wrap"></pre>
                </div>
                <div>
                    <h3 class="font-bold text-sky-400 mb-2">名詞庫</h3>
                    <pre id="summary-glossary" class="text-sm bg-gray-800 p-3 rounded-md whitespace-pre-wrap"></pre>
                </div>
            </div>
            <div class="p-6 border-t border-gray-700 mt-auto">
                <p class="text-sm text-gray-400 mb-4">AI 將根據以上所有設定，為你生成小說的第一章。這是後端處理的核心環節。</p>
                <button id="start-generation-btn" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg flex items-center justify-center gap-2 ai-button text-lg">
                    <i data-lucide="rocket" class="w-6 h-6"></i>
                    開始生成小說！
                </button>
                 <div id="generation-status" class="text-center mt-4 text-green-400 hidden"></div>
            </div>
        </div>
    </div>


    <!-- Main Application Scripts -->
    <script src="js/state.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/api.js"></script>
    <script src="js/prompts.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
