class API {
    constructor() {
        this.apiKey = null;
        this.elements = {
            apiKeyInput: document.getElementById('gemini-api-key'),
            statusDiv: document.getElementById('api-status'),
            statusText: document.getElementById('api-status-text'),
            testApiBtn: document.getElementById('test-api'),
            debugPanel: document.getElementById('debug-panel'),
            debugContent: document.getElementById('debug-content'),
            toggleDebugBtn: document.getElementById('toggle-debug'),
        };
    }

    /**
     * 初始化 API 模組
     */
    init() {
        this.loadAPISettings();
    }

    /**
     * 保存 API 設定
     */
    saveAPISettings() {
        const apiKey = this.elements.apiKeyInput.value.trim();
        if (!apiKey) {
            window.ui.showNotification('請先輸入 API Key', 'error');
            return;
        }
        try {
            // 關鍵修復：立即更新實例中�� apiKey
            this.apiKey = apiKey;
            
            const encodedKey = btoa(apiKey);
            localStorage.setItem('gemini_api_key', encodedKey);
            window.ui.showNotification('API 設定已保存，您現在可以使用AI功能了。', 'success');
        } catch (error) {
            window.ui.showNotification('保存失敗：' + error.message, 'error');
        }
    }

    /**
     * 載入 API 設定
     */
    loadAPISettings() {
        try {
            const encodedKey = localStorage.getItem('gemini_api_key');
            if (encodedKey) {
                const apiKey = atob(encodedKey);
                this.elements.apiKeyInput.value = apiKey;
                this.apiKey = apiKey;
                // 頁面加載時不再自動測試，讓用戶手動觸發
                // setTimeout(() => this.testAPIConnection(), 1000);
            }
        } catch (error) {
            console.error('載入API設定失敗:', error);
        }
    }

    /**
     * 測試 API 連接
     */
    async testAPIConnection() {
        // 關鍵修復：永遠直接從輸入框讀取最新值進行測試
        const apiKey = this.elements.apiKeyInput.value.trim();
        if (!apiKey) {
            window.ui.showNotification('請在輸入框中輸入 API Key 後再測試', 'error');
            return;
        }

        if (!apiKey.startsWith('AIza')) {
            this.elements.statusDiv.className = 'w-3 h-3 bg-red-500 rounded-full';
            this.elements.statusText.textContent = '格式錯誤';
            window.ui.showNotification('API Key 格式不正確，應該以 "AIza" 開頭', 'error');
            return;
        }

        this.elements.statusDiv.className = 'w-3 h-3 bg-yellow-500 rounded-full animate-pulse';
        this.elements.statusText.textContent = '測試中...';
        this.elements.testApiBtn.disabled = true;

        try {
            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents: [{ parts: [{ text: "Hello" }] }] })
            });

            if (response.ok) {
                this.elements.statusDiv.className = 'w-3 h-3 bg-green-500 rounded-full';
                this.elements.statusText.textContent = '已連接';
                // 關鍵修復：測試成功後，同步更新實例中的 apiKey
                this.apiKey = apiKey;
                window.ui.showNotification('API 連接成功！', 'success');
            } else {
                let errorMsg = `HTTP 錯誤碼: ${response.status}`;
                try {
                    const error = await response.json();
                    errorMsg = error.error.message;
                } catch (e) {
                    errorMsg = `無法解析錯誤回應，狀態碼: ${response.status}`;
                }
                this.elements.statusDiv.className = 'w-3 h-3 bg-red-500 rounded-full';
                this.elements.statusText.textContent = '連接失敗';
                window.ui.showNotification(`API 連接失敗: ${errorMsg}`, 'error');
            }
        } catch (error) {
            this.elements.statusDiv.className = 'w-3 h-3 bg-red-500 rounded-full';
            this.elements.statusText.textContent = '連接失敗';
            window.ui.showNotification(`測試失敗: ${error.message}. (提示: 可能是CORS或網絡問題，請按F12查��控制台)`, 'error');
            console.error("API Test Error:", error);
        } finally {
            this.elements.testApiBtn.disabled = false;
        }
    }
    
    /**
     * 檢查 API 狀態是否可用
     */
    isReady() {
        // 關鍵修復：檢查API是否準備就��的唯一標準是實例中的 apiKey 是否存在。
        // 連接狀態只是一個UI提示。
        return !!this.apiKey;
    }

    /**
     * 調用 Gemini API
     */
    async call(prompt) {
        if (!this.isReady()) {
            // 修正提示訊息，引導用戶去保存或測試
            window.ui.showNotification('請先輸入API Key並點擊「保存設定」或「測試連接」', 'error');
            window.ui.handleTabSwitch('core');
            throw new Error('API key is not set.');
        }

        try {
            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    contents: [{ parts: [{ text: prompt }] }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 2048,
                    }
                })
            });

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}`;
                try {
                    const errorData = await response.json();
                    if (errorData.error && errorData.error.message) {
                        errorMessage = errorData.error.message;
                    }
                } catch (parseError) {
                    console.warn('Could not parse error response:', parseError);
                }

                // 提供更友好的錯誤信息
                if (response.status === 403) {
                    errorMessage = 'API權限不足，請檢查API Key是否正確或服務是否已啟用';
                } else if (response.status === 429) {
                    errorMessage = 'API調用頻率過高，請稍後再試';
                } else if (response.status === 404) {
                    errorMessage = 'API端點不存在，請檢查API Key是否來自正確的服務';
                }

                throw new Error(`API Error (${response.status}): ${errorMessage}`);
            }

            const data = await response.json();
            if (data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0]) {
                return data.candidates[0].content.parts[0].text;
            } else {
                console.error('Unexpected API response format:', data);
                throw new Error('API回應格式異常，請稍後再試');
            }
        } catch (error) {
            console.error('Gemini API call error:', error);

            // 如果是網絡錯誤，提供更友好的提示
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('網絡連接失敗，請檢查網絡連接或嘗試使用VPN');
            }

            throw error;
        }
    }
    
    // ... ��以將診斷、調試等功能也移到這裡
    toggleDebugPanel() {
        const panel = this.elements.debugPanel;
        const button = this.elements.toggleDebugBtn;
        const isHidden = panel.classList.toggle('hidden');
        button.textContent = isHidden ? '顯示調試信息' : '隱藏調試信息';
    }

    addDebugLog(message, type = 'info') {
        const content = this.elements.debugContent;
        const logEntry = document.createElement('div');
        const typeColors = {
            info: 'text-blue-300',
            success: 'text-green-300',
            error: 'text-red-300',
        };
        logEntry.className = `mb-1 ${typeColors[type] || 'text-gray-300'}`;
        logEntry.innerHTML = `<span class="text-gray-500">[${new Date().toLocaleTimeString()}]</span> ${message}`;
        content.appendChild(logEntry);
        content.scrollTop = content.scrollHeight;
    }
    
    clearDebugLog() {
        this.elements.debugContent.innerHTML = '';
    }

    async testAllEndpoints() {
        if (!this.apiKey) {
            this.addDebugLog('❌ 請先輸入並保存 API Key', 'error');
            return;
        }
        this.clearDebugLog();
        this.addDebugLog('--- 開始全面診斷 ---', 'info');
        this.addDebugLog(`使用的模型: gemini-1.5-pro`, 'info');

        try {
            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro?key=${this.apiKey}`);
            this.addDebugLog(`模型資訊端點 (/models) 狀態: ${response.status}`, response.ok ? 'success' : 'error');
            if(response.ok) {
                const data = await response.json();
                this.addDebugLog(`模型顯示名稱: ${data.displayName}`, 'success');
                this.addDebugLog(`支持的方法: ${data.supportedGenerationMethods.join(', ')}`, 'success');
            } else {
                 this.addDebugLog(`錯誤訊息: ${await response.text()}`, 'error');
            }

        } catch (error) {
            this.addDebugLog(`請求異常: ${error.message}`, 'error');
        }
        this.addDebugLog('--- 診斷完成 ---', 'info');
    }
}
