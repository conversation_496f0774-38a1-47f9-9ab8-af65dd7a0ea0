class AppState {
    constructor() {
        this.fieldsToSave = [
            // 核心設定
            'novel-title', 'novel-genre', 'writing-style', 'story-core', 'ai-idea',
            // 世界觀設定
            'world-geography', 'world-climate', 'world-locations',
            'power-system', 'power-source', 'power-rules',
            'tech-level', 'civilization', 'social-structure', 'organizations', 'world-rules',
            // 名詞庫
            'characters-main', 'characters-supporting',
            'locations-major', 'locations-minor',
            'items-weapons', 'items-consumables',
            'skills-cultivation', 'skills-combat',
            'organizations-righteous', 'organizations-evil',
            'terms-cultivation', 'terms-world'
        ];
        this.data = {};
        this.characterCount = 0;
    }

    // 從 DOM 加載數據到 state
    loadStateFromDOM() {
        // 加載基本字段
        this.fieldsToSave.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.data[id] = element.value;
            }
        });
        // 加載角色數據
        this.data.characters = this.getCharactersDataFromDOM();
        // 加載大綱數據
        this.data.outline = this.getOutlineDataFromDOM();
    }

    // 保存 state 到 localStorage
    saveStateToLocalStorage() {
        try {
            // 檢查localStorage是否可用
            if (typeof(Storage) === "undefined") {
                throw new Error('瀏覽器不支援localStorage');
            }

            // 檢查數據大小（localStorage通常有5-10MB限制）
            const dataString = JSON.stringify(this.data);
            if (dataString.length > 5 * 1024 * 1024) { // 5MB
                console.warn('數據量較大，可能接近localStorage限制');
                window.ui.showNotification('數據量較大，建議定期導出備份', 'warning');
            }

            localStorage.setItem('novelGeneratorData', dataString);
            console.log("State saved to localStorage");
        } catch (error) {
            console.error('保存數據到 localStorage 時發生錯誤:', error);
            if (error.name === 'QuotaExceededError') {
                window.ui.showNotification('存儲空間不足，請清理瀏覽器數據或導出專案', 'error');
            } else {
                window.ui.showNotification('保存數據時發生錯誤：' + error.message, 'error');
            }
        }
    }

    // 從 localStorage 加載 state
    loadStateFromLocalStorage() {
        try {
            if (typeof(Storage) === "undefined") {
                console.warn('瀏覽器不支援localStorage');
                return;
            }

            const storedData = localStorage.getItem('novelGeneratorData');
            if (storedData) {
                // 驗證JSON格式
                const parsedData = JSON.parse(storedData);

                // 基本數據驗證
                if (typeof parsedData === 'object' && parsedData !== null) {
                    this.data = parsedData;
                    console.log('Successfully loaded data from localStorage');
                } else {
                    console.warn('Invalid data format in localStorage');
                    this.data = {};
                }
            }
        } catch (error) {
            console.error('從 localStorage 載入數據時發生錯誤:', error);
            if (error instanceof SyntaxError) {
                console.warn('localStorage中的數據格式損壞，將重置為空');
                localStorage.removeItem('novelGeneratorData');
                this.data = {};
                window.ui.showNotification('本地數據損壞已重置，請重新開始或導入備份', 'warning');
            } else {
                window.ui.showNotification('載入本地數據時發生錯誤：' + error.message, 'error');
            }
        }
    }

    // 將 state 應用到 DOM
    applyStateToDOM() {
        // 應用基本字段
        this.fieldsToSave.forEach(id => {
            const element = document.getElementById(id);
            if (element && this.data[id]) {
                element.value = this.data[id];
            }
        });

        // 應用角色
        const characterList = document.getElementById('character-list');
        characterList.innerHTML = '';
        this.characterCount = 0;
        if (this.data.characters && this.data.characters.length > 0) {
            this.data.characters.forEach(charData => {
                window.ui.addCharacterCard(charData);
            });
        } else {
            window.ui.addCharacterCard();
        }

        // 應用大綱
        window.ui.renderOutline(this.data.outline || []);
    }
    
    // 從 DOM 獲取角色數據
    getCharactersDataFromDOM() {
        const characters = [];
        document.querySelectorAll('.character-card').forEach(card => {
            const charData = {};
            card.querySelectorAll('input, textarea').forEach(input => {
                if (input.dataset.field) {
                    charData[input.dataset.field] = input.value;
                }
            });
            characters.push(charData);
        });
        return characters;
    }

    // 從 DOM 獲取大綱數據
    getOutlineDataFromDOM() {
        const volumes = [];
        document.querySelectorAll('.volume-card').forEach(volumeEl => {
            const volumeData = {
                id: volumeEl.dataset.id,
                title: volumeEl.querySelector('.volume-title').value,
                description: volumeEl.querySelector('.volume-description textarea').value,
                chapters: []
            };
            volumeEl.querySelectorAll('.chapter-card').forEach(chapterEl => {
                const chapterData = {
                    id: chapterEl.dataset.id,
                    title: chapterEl.querySelector('.chapter-title').value,
                    scene: chapterEl.querySelector('[data-field="scene"]').value,
                    goal: chapterEl.querySelector('[data-field="goal"]').value,
                };
                volumeData.chapters.push(chapterData);
            });
            volumes.push(volumeData);
        });
        return volumes;
    }

    // 獲取整個數據對象以供導出
    getDataForExport() {
        this.loadStateFromDOM(); // 確保 state 是最新的
        return this.data;
    }

    // 從導入的數據加載 state
    loadStateFromImport(importedData) {
        this.data = importedData;
        this.saveStateToLocalStorage();
        this.applyStateToDOM();
    }
}
